<div class="nav-container">
    <div class="small-container">
        <div class="course-nav">
            <div class="progress">
                <ul>
                    <li><h2>Resume Learning</h2></li>
                </ul>
            </div>

            <div class="explore-course-filter">
                <!-- Search bar for Course Name -->
                <div class="filter-input">
                    <input 
                        type="text" 
                        name="course_name" 
                        id="filterByCourseName" 
                        placeholder="Search by Course Name" 
                        aria-label="Search by Course Name"
                        value="<?php echo isset($_GET['course_name']) ? htmlspecialchars($_GET['course_name'], ENT_QUOTES, 'UTF-8') : ''; ?>"
                    />
                </div>

                <!-- Date input for Created Date -->
                <div class="filter-input">
                    <input 
                        type="date" 
                        name="created_at" 
                        id="filterByDateCreated" 
                        aria-label="Filter by Date Created"
                        value="<?php echo isset($_GET['created_at']) ? htmlspecialchars($_GET['created_at'], ENT_QUOTES, 'UTF-8') : ''; ?>"
                    />
                </div>
                
            </div>
         
        </div>

        <?php $userID = $this->session->userdata('id'); ?>

        <div class="course-card-container">

            <?php if (!$logged_in): ?>
                <h3 class="text-not-login">Sign in to access courses and view your progress</h3>
            <?php endif;?>
            

            <?php foreach ($exams as $exam): ?>
                <div class="course-card"
                data-created-at="<?php echo date('Y-m-d', strtotime($exam['created_at'])); ?>">

                    <div class="course-content-title">
                        <h2><?php echo htmlspecialchars($exam['course_name'], ENT_QUOTES, 'UTF-8'); ?></h2>
                        <p><?php echo htmlspecialchars($exam['designation'], ENT_QUOTES, 'UTF-8'); ?></p>
                        <p><?php echo htmlspecialchars($exam['description'], ENT_QUOTES, 'UTF-8'); ?></p>

                        <!-- Display progress text and progress bar -->
                        <div class="progress-resume-container">
                            <p class="progress-resume"> 
                                <?php 
                                    $progress = $exam['progress'] !== null ? $exam['progress'] : 0;
                                    echo htmlspecialchars($progress . '%', ENT_QUOTES, 'UTF-8'); 
                                ?>
                                Completed
                            </p>
                            <progress value="<?php echo $progress; ?>" max="100"></progress>
                        </div>
                    </div>

                    <?php
                        $logged_in = $this->session->userdata('email');
                        $userID = $this->session->userdata('id');
                    ?>

                    <?php if ($logged_in): ?>
                        <div class="course-content-button">
                            <button class="start-exam-button inprogress-btn" data-exam-id="<?php echo $exam['module_id']; ?>" aria-label="Start Exam" onclick="startExam('<?php echo $exam['module_id']; ?>')">
                                Continue Learning &#8594;
                            </button>
                        </div>
                    <?php else: ?>
                        <!-- Handle case when user is not logged in -->
                        <div class="course-content-button">
                            <button id="start-exam-btn" class="inprogress-btn">Sign in to start this exam</button>
                        </div>
                    <?php endif; ?>

                </div>
            <?php endforeach; ?>
        </div>

    </div>
</div>



<script>
    document.addEventListener('DOMContentLoaded', () => {
        const courseSearch = document.getElementById('filterByCourseName');
        const dateFilter = document.getElementById('filterByDateCreated');
        const courseItems = document.querySelectorAll('.course-card');

        // Filter by course name
        courseSearch.addEventListener('input', () => {
            const query = courseSearch.value.toLowerCase();

            courseItems.forEach(item => {
                const courseName = item.querySelector('h2').textContent.toLowerCase();
                item.style.display = courseName.includes(query) ? '' : 'none';
            });
            updateURL();
        });

        // Filter by created date
        dateFilter.addEventListener('change', () => {
            const selectedDate = dateFilter.value;

            courseItems.forEach(item => {
                const courseDate = item.getAttribute('data-created-at');
                if (!selectedDate || courseDate === selectedDate) {
                    item.style.display = '';
                } else {
                    item.style.display = 'none';
                }
            });
            updateURL();
        });

        // Update URL when filters change
        function updateURL() {
            const url = new URL(window.location.href);
            const courseName = courseSearch.value;
            const createdAt = dateFilter.value;

            if (courseName) url.searchParams.set('course_name', courseName);
            else url.searchParams.delete('course_name');

            if (createdAt) url.searchParams.set('created_at', createdAt);
            else url.searchParams.delete('created_at');

            // Update the URL without reloading the page
            window.history.pushState({}, '', url);
        }

        // Ensure that clearing the date or search input resets the filters
        courseSearch.addEventListener('input', () => {
            if (courseSearch.value === '') {
                courseItems.forEach(item => {
                    item.style.display = '';
                });
                updateURL();
            }
        });

        dateFilter.addEventListener('input', () => {
            if (dateFilter.value === '') {
                courseItems.forEach(item => {
                    item.style.display = '';
                });
                updateURL();
            }
        });
    });
</script>


<script>
    function startExam(examId) {
        try {
            // Redirect to the exam page or handle exam start logic here
            window.location.href = "<?php echo site_url('courses/view/'); ?>" + examId;
        } catch (error) {
            console.error('Error starting exam:', error);
            alert('Error starting exam. Please try again.');
        }
    }
</script>


<script>
    document.addEventListener("DOMContentLoaded", function() {
        // resume.php:(56-85)
        var startExamBtn = document.getElementById('start-exam-btn');
        var loginPanel = document.getElementById('loginPanel');

        if (startExamBtn) {
            startExamBtn.addEventListener('click', function(event) {
                try {
                    // Prevent default action for button if not logged in
                    if (!<?php echo json_encode($logged_in); ?>) {
                        event.preventDefault(); // Stop navigation or default action

                        if (loginPanel) {
                            loginPanel.style.marginRight = '0'; // Reveal the login panel
                        } else {
                            console.error('Error: loginPanel element not found');
                        }
                    }
                } catch (error) {
                    console.error('Error handling start exam button click:', error);
                    SmartAlerts.error('An error occurred while starting the exam. Please refresh the page and try again.', 'Exam Start Error');
                }
            });
        } 
        else {
            console.log('startExamBtn element not found');
        }
    });
</script>


<script>
    document.addEventListener('DOMContentLoaded', (event) => {
        const courseCardContainer = document.querySelector('.nav-container');
        if (courseCardContainer) {
            const textElements = courseCardContainer.querySelectorAll('small, p, h4, .course-card-container');
            textElements.forEach(element => {
                element.addEventListener('mousedown', (e) => {
                    e.preventDefault();
                });
            });
        }
    });
</script>

<script>
    function disableTextSelection() {
        const elements = document.querySelectorAll('.course-card-container');
        elements.forEach(element => {
            element.style.userSelect = 'none'; // For modern browsers
            element.style.MozUserSelect = 'none'; // For Firefox
            element.style.msUserSelect = 'none'; // For Internet Explorer and Edge
            element.style.webkitUserSelect = 'none'; // For Safari
        });
    }

    // Call the function to apply the style
    disableTextSelection();
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const container = document.querySelector('.course-card-container');
        let isDown = false;
        let startX;
        let scrollLeft;

        container.addEventListener('mousedown', (e) => {
            isDown = true;
            container.classList.add('active');
            startX = e.pageX - container.offsetLeft;
            scrollLeft = container.scrollLeft;
        });

        container.addEventListener('mouseleave', () => {
            isDown = false;
            container.classList.remove('active');
        });

        container.addEventListener('mouseup', () => {
            isDown = false;
            container.classList.remove('active');
        });

        container.addEventListener('mousemove', (e) => {
            if (!isDown) return; // stop the fn from running
            e.preventDefault();
            const x = e.pageX - container.offsetLeft;
            const walk = (x - startX) * 2; // scroll-fast
            container.scrollLeft = scrollLeft - walk;
        });
    });
</script>