/**
 * Smart Floating Alert System
 * Replaces traditional JavaScript alerts with modern, user-friendly floating notifications
 */

class SmartAlerts {
    constructor() {
        this.container = null;
        this.alerts = new Map();
        this.alertCounter = 0;
        this.isReady = false;

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    init() {
        // Create container if it doesn't exist
        if (!document.querySelector('.smart-alert-container')) {
            this.container = document.createElement('div');
            this.container.className = 'smart-alert-container';
            document.body.appendChild(this.container);
        } else {
            this.container = document.querySelector('.smart-alert-container');
        }
        this.isReady = true;
        console.log('SmartAlerts initialized successfully');
    }

    /**
     * Show a smart alert
     * @param {string} message - The alert message
     * @param {string} type - Alert type: 'success', 'error', 'warning', 'info'
     * @param {string} title - Optional title for the alert
     * @param {number} duration - Duration in milliseconds (0 for persistent)
     * @param {boolean} closable - Whether the alert can be manually closed
     */
    show(message, type = 'info', title = '', duration = 5000, closable = true) {
        // Wait for initialization if not ready
        if (!this.isReady) {
            setTimeout(() => this.show(message, type, title, duration, closable), 100);
            return;
        }

        const alertId = ++this.alertCounter;

        // Create alert element
        const alertElement = this.createAlertElement(alertId, message, type, title, closable);

        // Add to container
        this.container.appendChild(alertElement);

        // Store alert reference
        this.alerts.set(alertId, {
            element: alertElement,
            timer: null
        });

        // Trigger show animation
        setTimeout(() => {
            alertElement.classList.add('show');
        }, 10);

        // Auto-hide after duration
        if (duration > 0) {
            this.startAutoHide(alertId, duration);
        }

        return alertId;
    }

    createAlertElement(id, message, type, title, closable) {
        const alert = document.createElement('div');
        alert.className = `smart-alert ${type}`;
        alert.dataset.alertId = id;

        // Get appropriate icon and default title
        const { icon, defaultTitle } = this.getAlertConfig(type);
        const alertTitle = title || defaultTitle;

        alert.innerHTML = `
            <div class="smart-alert-icon">${icon}</div>
            <div class="smart-alert-content">
                <div class="smart-alert-title">${alertTitle}</div>
                <div class="smart-alert-message">${message}</div>
            </div>
            ${closable ? '<button class="smart-alert-close" aria-label="Close">&times;</button>' : ''}
            <div class="smart-alert-progress" style="width: 100%"></div>
        `;

        // Add close functionality
        if (closable) {
            const closeBtn = alert.querySelector('.smart-alert-close');
            closeBtn.addEventListener('click', () => this.hide(id));
        }

        return alert;
    }

    getAlertConfig(type) {
        const configs = {
            success: { icon: '✓', defaultTitle: 'Success' },
            error: { icon: '✕', defaultTitle: 'Error' },
            warning: { icon: '⚠', defaultTitle: 'Warning' },
            info: { icon: 'i', defaultTitle: 'Information' }
        };
        return configs[type] || configs.info;
    }

    startAutoHide(alertId, duration) {
        const alertData = this.alerts.get(alertId);
        if (!alertData) return;

        const progressBar = alertData.element.querySelector('.smart-alert-progress');
        
        // Animate progress bar
        if (progressBar) {
            progressBar.style.transition = `width ${duration}ms linear`;
            progressBar.style.width = '0%';
        }

        // Set timer to hide alert
        alertData.timer = setTimeout(() => {
            this.hide(alertId);
        }, duration);
    }

    hide(alertId) {
        const alertData = this.alerts.get(alertId);
        if (!alertData) return;

        // Clear timer if exists
        if (alertData.timer) {
            clearTimeout(alertData.timer);
        }

        // Add hide animation
        alertData.element.classList.add('hide');

        // Remove from DOM after animation
        setTimeout(() => {
            if (alertData.element.parentNode) {
                alertData.element.parentNode.removeChild(alertData.element);
            }
            this.alerts.delete(alertId);
        }, 300);
    }

    hideAll() {
        this.alerts.forEach((_, alertId) => {
            this.hide(alertId);
        });
    }

    // Convenience methods
    success(message, title = '', duration = 4000) {
        return this.show(message, 'success', title, duration);
    }

    error(message, title = '', duration = 6000) {
        return this.show(message, 'error', title, duration);
    }

    warning(message, title = '', duration = 5000) {
        return this.show(message, 'warning', title, duration);
    }

    info(message, title = '', duration = 5000) {
        return this.show(message, 'info', title, duration);
    }
}

// Initialize when DOM is ready
function initializeSmartAlerts() {
    // Create global instance
    window.SmartAlerts = new SmartAlerts();

    // Override the default alert function
    window.originalAlert = window.alert;
    window.alert = function(message) {
        // Determine alert type based on message content
        let type = 'info';
        let title = '';

        if (message.toLowerCase().includes('error') || message.toLowerCase().includes('failed') || message.toLowerCase().includes('network')) {
            type = 'error';
            title = 'Error';
        } else if (message.toLowerCase().includes('success') || message.toLowerCase().includes('completed') || message.toLowerCase().includes('marked as')) {
            type = 'success';
            title = 'Success';
        } else if (message.toLowerCase().includes('please') || message.toLowerCase().includes('select') || message.toLowerCase().includes('provide')) {
            type = 'warning';
            title = 'Action Required';
        }

        // Show smart alert instead of browser alert
        window.SmartAlerts.show(message, type, title);
    };

    console.log('SmartAlerts system ready!');
}

// Initialize immediately if DOM is ready, otherwise wait
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeSmartAlerts);
} else {
    initializeSmartAlerts();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SmartAlerts;
}
