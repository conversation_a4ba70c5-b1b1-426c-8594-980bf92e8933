<div class="main-container">
   <div class="admin-mngt-header">
      <h4>Permissions Management</h4>
      <input type="text" id="searchInput" placeholder="Search users or admins...">
      <select id="departmentFilter">
         <option value="">Filter by Department</option>
         <?php
         $departments = array_unique(array_map(function ($user) {
            return $user->department;
         }, $users));
         foreach ($departments as $department): ?>
            <option value="<?php echo htmlspecialchars($department); ?>">
               <?php echo htmlspecialchars($department); ?>
            </option>
         <?php endforeach; ?>
      </select>
   </div>

   <div class="user-admin-container">
      <!-- Users Section -->
      <div class="user-container mngt-item" data-aos="fade-up">
         <h4>Grant Admin Access</h4>
         <div id="usersList"></div>
         <div id="pagination"></div>
      </div>

      <!-- Admins Section -->
      <div class="admin-container mngt-item" data-aos="fade-left">
         <h4>BLS Admins</h4>
         <div id="adminsList"></div>
      </div>
   </div>
</div>

<!-- Confirmation Modal -->
<div id="confirmationModal" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background-color: white; padding: 20px; border: 1px solid #ccc; z-index: 1000;">
   <h4 id="modalTitle">Confirm Action</h4>
   <p id="modalDetails"></p>
   <button id="confirmAction">Confirm</button>
   <button id="cancelAction">Cancel</button>
</div>

<div id="modalOverlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); z-index: 999;"></div>

<script>
   const users = <?php echo json_encode($users); ?>; // All users and admins from PHP
   const usersPerPage = 24;
   let currentPage = 1;
   let pendingAction = null;

   // Function to open the confirmation modal
   function openModal(user, action) {
      const modal = document.getElementById('confirmationModal');
      const overlay = document.getElementById('modalOverlay');

      const fullName = `<u>${user.first_name} ${user.last_name}</u>`;
      const department = `<u>${user.department}</u>`;
      const email = `<u>${user.email}</u>`;

      const actionText = action === 'make' ? 'Grant Admin Access Confirmation' : 'Confirm Remove Admin Access';
      const sentence = `${actionText} for ${fullName} from ${department} with this email ${email}.`;
      const messageText = action === 'make' ?
         'Are you sure you want to grant admin access to this user?' :
         'Are you sure you want to remove admin access from this user?';

      document.getElementById('modalTitle').innerHTML = sentence;
      document.getElementById('modalDetails').innerHTML = `<p>${messageText}</p>`;

      // Store the pending action
      pendingAction = {
         user,
         action
      };

      modal.style.display = 'block';
      overlay.style.display = 'block';
   }



   // Function to close the modal
   function closeModal() {
      document.getElementById('confirmationModal').style.display = 'none';
      document.getElementById('modalOverlay').style.display = 'none';
      pendingAction = null;
   }

   // Function to confirm the action
   document.getElementById('confirmAction').addEventListener('click', () => {
      if (pendingAction) {
         const {
            user,
            action
         } = pendingAction;

         // Send AJAX request to perform the action
         fetch('<?php echo site_url('admin_management/toggle_admin'); ?>', {
               method: 'POST',
               headers: {
                  'Content-Type': 'application/json'
               },
               body: JSON.stringify({
                  user_id: user.id,
                  action: action
               })
            })
            .then(response => response.json())
            .then(data => {
               if (data.success) {
                  SmartAlerts.success('Admin access has been updated successfully.', 'Role Updated');
                  setTimeout(() => {
                     location.reload(); // Reload the page to reflect changes
                  }, 1500);
               } else {
                  SmartAlerts.error(data.error || 'Unable to update role. Please try again.', 'Update Failed');
               }
            })
            .catch(error => console.error('Error:', error));

         closeModal();
      }
   });

   // Cancel the action
   document.getElementById('cancelAction').addEventListener('click', closeModal);

   // Function to display users with pagination
   function displayUsers() {
      const filteredUsers = getFilteredData('user');
      const startIndex = (currentPage - 1) * usersPerPage;
      const paginatedUsers = filteredUsers.slice(startIndex, startIndex + usersPerPage);

      const usersList = document.getElementById('usersList');
      usersList.innerHTML = '';

      paginatedUsers.forEach(user => {
         const userDiv = document.createElement('div');
         userDiv.classList.add('user-item');

         userDiv.innerHTML = `
                <div class="user-info">
                    <p class="user-info-item">${user.first_name} ${user.last_name}</p>
                    <p class="user-info-item">${formatDepartment(user.department)}</p>
                    <p class="user-info-item user-info-email">${user.email}</p>
                </div>
                <button data-action="make">Grant Admin Access</button>
            `;
         const button = userDiv.querySelector('button');
         button.addEventListener('click', () => openModal(user, 'make'));
         usersList.appendChild(userDiv);
      });

      renderPagination(filteredUsers.length);
   }

   // Function to display admins
   // Function to display admins with animation delay
   function displayAdmins() {
      const filteredAdmins = getFilteredData('admin');
      const adminsList = document.getElementById('adminsList');
      adminsList.innerHTML = '';

      filteredAdmins.forEach((admin, index) => {
         setTimeout(() => {
            const adminDiv = document.createElement('div');
            adminDiv.classList.add('admin-item');
            adminDiv.setAttribute('data-aos', 'fade-left');
            adminDiv.setAttribute('data-aos-delay', `${index * 100}`); // 100ms delay incrementally

            adminDiv.innerHTML = `
                    <div>
                        <h5>${admin.first_name} ${admin.last_name}</h5>
                        <p>Department: ${formatDepartment(admin.department)}</p>
                        <p>Email: ${admin.email}</p>
                    </div>
                    <button data-action="remove">Remove Admin</button>
                `;

            const button = adminDiv.querySelector('button');
            button.addEventListener('click', () => openModal(admin, 'remove'));

            adminsList.appendChild(adminDiv);
         }, index * 100); // Delay each item by 100ms incrementally
      });

      AOS.refresh(); // Refresh AOS to apply animations dynamically
   }

   function formatDepartment(dept) {
      // Split by comma in case there are multiple departments
      return dept.split(',').map(part => {
         // Split by dash
         return part.split('-').map(segment => {
            segment = segment.trim();

            // Acronym: all caps if 2+ uppercase letters or short word
            if (/^[A-Za-z]{2,5}$/.test(segment) && segment === segment.toUpperCase()) {
               return segment.toUpperCase();
            }

            if (/^[A-Za-z]{2,5}$/.test(segment)) {
               return segment.toUpperCase(); // Normalize short acronyms (e.g., 'bod' → 'BOD')
            }

            // Else capitalize each word (e.g., 'brigada group' → 'Brigada Group')
            return segment
               .toLowerCase()
               .split(' ')
               .map(word => word.charAt(0).toUpperCase() + word.slice(1))
               .join(' ');
         }).join(' - ');
      }).join(', ');
   }




   // Filter and search logic (same as before)
   function getFilteredData(role) {
      const searchQuery = document.getElementById('searchInput').value.toLowerCase();
      const departmentFilter = document.getElementById('departmentFilter').value;

      return users.filter(user => {
         const matchesSearch = user.first_name.toLowerCase().includes(searchQuery) ||
            user.last_name.toLowerCase().includes(searchQuery) ||
            user.email.toLowerCase().includes(searchQuery);
         const matchesDepartment = departmentFilter === '' || user.department === departmentFilter;
         return user.role === role && matchesSearch && matchesDepartment;
      });
   }

   function renderPagination(totalUsers) {
      const totalPages = Math.ceil(totalUsers / usersPerPage);
      const paginationDiv = document.getElementById('pagination');
      paginationDiv.innerHTML = '';

      // Previous button
      const prevButton = document.createElement('button');
      prevButton.textContent = 'Previous';
      prevButton.disabled = currentPage === 1;
      prevButton.addEventListener('click', () => {
         if (currentPage > 1) {
            currentPage--;
            displayUsers();
         }
      });
      paginationDiv.appendChild(prevButton);

      // Page buttons (limit to 5 page numbers)
      const pageStart = Math.max(1, currentPage - 2);
      const pageEnd = Math.min(totalPages, currentPage + 2);

      for (let i = pageStart; i <= pageEnd; i++) {
         const pageButton = document.createElement('button');
         pageButton.textContent = i;
         pageButton.disabled = i === currentPage;
         pageButton.className = i === currentPage ? 'active' : ''; // Add class 'active' to the current page button
         pageButton.addEventListener('click', () => {
            currentPage = i;
            displayUsers();
         });
         paginationDiv.appendChild(pageButton);
      }

      // Next button
      const nextButton = document.createElement('button');
      nextButton.textContent = 'Next';
      nextButton.disabled = currentPage === totalPages;
      nextButton.addEventListener('click', () => {
         if (currentPage < totalPages) {
            currentPage++;
            displayUsers();
         }
      });
      paginationDiv.appendChild(nextButton);
   }

   document.getElementById('searchInput').addEventListener('input', displayData);
   document.getElementById('departmentFilter').addEventListener('change', displayData);

   function displayData() {
      displayUsers();
      displayAdmins();
   }

   displayData();
</script>


<script>
   AOS.init();
</script>