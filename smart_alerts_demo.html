<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Alerts Demo</title>
    <link rel="stylesheet" href="css/header.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: #f8fafc;
        }
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        h1 {
            color: #1f2937;
            margin-bottom: 8px;
        }
        .subtitle {
            color: #6b7280;
            margin-bottom: 40px;
        }
        .demo-section {
            margin-bottom: 30px;
        }
        .demo-section h3 {
            color: #374151;
            margin-bottom: 15px;
        }
        .button-group {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .demo-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }
        .demo-btn.success {
            background: #10b981;
            color: white;
        }
        .demo-btn.error {
            background: #ef4444;
            color: white;
        }
        .demo-btn.warning {
            background: #f59e0b;
            color: white;
        }
        .demo-btn.info {
            background: #3b82f6;
            color: white;
        }
        .demo-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .old-alert-btn {
            background: #6b7280;
            color: white;
        }
        .code-block {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            color: #374151;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>Smart Alerts System Demo</h1>
        <p class="subtitle">Modern floating alerts that replace traditional JavaScript alerts</p>

        <div class="demo-section">
            <h3>Alert Types</h3>
            <div class="button-group">
                <button class="demo-btn success" onclick="SmartAlerts.success('Operation completed successfully!', 'Success')">Success Alert</button>
                <button class="demo-btn error" onclick="SmartAlerts.error('Something went wrong. Please try again.', 'Error')">Error Alert</button>
                <button class="demo-btn warning" onclick="SmartAlerts.warning('Please fill in all required fields.', 'Warning')">Warning Alert</button>
                <button class="demo-btn info" onclick="SmartAlerts.info('Here is some helpful information.', 'Information')">Info Alert</button>
            </div>
        </div>

        <div class="demo-section">
            <h3>Real-world Examples</h3>
            <div class="button-group">
                <button class="demo-btn success" onclick="SmartAlerts.success('Module has been reset successfully.', 'Module Reset')">Module Reset</button>
                <button class="demo-btn warning" onclick="SmartAlerts.warning('Please provide feedback before retaking the module.', 'Feedback Required')">Feedback Required</button>
                <button class="demo-btn error" onclick="SmartAlerts.error('A network error occurred. Please check your connection and try again.', 'Network Error')">Network Error</button>
                <button class="demo-btn info" onclick="SmartAlerts.info('Notification has been marked as read.', 'Notification Updated')">Notification Update</button>
            </div>
        </div>

        <div class="demo-section">
            <h3>Comparison with Old Alerts</h3>
            <div class="button-group">
                <button class="demo-btn old-alert-btn" onclick="window.originalAlert('This is the old alert style')">Old Alert (Browser)</button>
                <button class="demo-btn info" onclick="SmartAlerts.info('This is the new smart alert style with better UX!', 'Modern Alert')">New Smart Alert</button>
            </div>
        </div>

        <div class="demo-section">
            <h3>Features</h3>
            <ul>
                <li><strong>Non-blocking:</strong> Users can continue interacting with the page</li>
                <li><strong>Auto-dismiss:</strong> Alerts automatically disappear after a set time</li>
                <li><strong>Manual close:</strong> Users can close alerts manually</li>
                <li><strong>Contextual icons:</strong> Different icons for different alert types</li>
                <li><strong>Progress indicator:</strong> Shows remaining time before auto-dismiss</li>
                <li><strong>Smooth animations:</strong> Elegant slide-in and slide-out effects</li>
                <li><strong>Responsive:</strong> Works well on all device sizes</li>
                <li><strong>Accessible:</strong> Proper ARIA labels and keyboard support</li>
            </ul>
        </div>

        <div class="demo-section">
            <h3>Usage Examples</h3>
            <div class="code-block">
// Basic usage
SmartAlerts.success('Operation completed!');
SmartAlerts.error('Something went wrong');
SmartAlerts.warning('Please check your input');
SmartAlerts.info('Here is some information');

// With custom titles and duration
SmartAlerts.success('Data saved successfully', 'Save Complete', 3000);
SmartAlerts.error('Failed to connect to server', 'Connection Error', 0); // Persistent

// The old alert() function is automatically replaced
alert('This will show as a smart alert!');
            </div>
        </div>

        <div class="demo-section">
            <h3>Test Multiple Alerts</h3>
            <button class="demo-btn info" onclick="showMultipleAlerts()">Show Multiple Alerts</button>
        </div>
    </div>

    <script src="assets/js/smart-alerts.js"></script>
    <script>
        function showMultipleAlerts() {
            SmartAlerts.info('First alert', 'Alert 1');
            setTimeout(() => SmartAlerts.success('Second alert', 'Alert 2'), 500);
            setTimeout(() => SmartAlerts.warning('Third alert', 'Alert 3'), 1000);
            setTimeout(() => SmartAlerts.error('Fourth alert', 'Alert 4'), 1500);
        }
    </script>
</body>
</html>
