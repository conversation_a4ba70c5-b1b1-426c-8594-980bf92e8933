<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Smart Alerts</title>
    <style>
        /*----Smart Floating Alerts------*/
        .smart-alert-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        }

        .smart-alert {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            margin-bottom: 12px;
            padding: 16px 20px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
            transform: translateX(420px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-left: 4px solid;
            position: relative;
            overflow: hidden;
        }

        .smart-alert.show {
            transform: translateX(0);
        }

        .smart-alert.hide {
            transform: translateX(420px);
            opacity: 0;
        }

        .smart-alert.success {
            border-left-color: #10b981;
            background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
        }

        .smart-alert.error {
            border-left-color: #ef4444;
            background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
        }

        .smart-alert.warning {
            border-left-color: #f59e0b;
            background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
        }

        .smart-alert.info {
            border-left-color: #3b82f6;
            background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
        }

        .smart-alert-icon {
            flex-shrink: 0;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #fff;
            font-weight: bold;
        }

        .smart-alert.success .smart-alert-icon {
            background: #10b981;
        }

        .smart-alert.error .smart-alert-icon {
            background: #ef4444;
        }

        .smart-alert.warning .smart-alert-icon {
            background: #f59e0b;
        }

        .smart-alert.info .smart-alert-icon {
            background: #3b82f6;
        }

        .smart-alert-content {
            flex: 1;
            min-width: 0;
        }

        .smart-alert-title {
            font-weight: 600;
            font-size: 14px;
            margin: 0 0 4px 0;
            color: #1f2937;
            line-height: 1.4;
        }

        .smart-alert-message {
            font-size: 13px;
            color: #6b7280;
            margin: 0;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .smart-alert-close {
            position: absolute;
            top: 8px;
            right: 8px;
            background: none;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.2s;
            font-size: 16px;
            line-height: 1;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .smart-alert-close:hover {
            background: rgba(0, 0, 0, 0.05);
            color: #374151;
        }

        .smart-alert-progress {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: rgba(0, 0, 0, 0.1);
            transition: width linear;
        }

        .smart-alert.success .smart-alert-progress {
            background: #10b981;
        }

        .smart-alert.error .smart-alert-progress {
            background: #ef4444;
        }

        .smart-alert.warning .smart-alert-progress {
            background: #f59e0b;
        }

        .smart-alert.info .smart-alert-progress {
            background: #3b82f6;
        }

        /* Demo styles */
        body {
            font-family: Arial, sans-serif;
            padding: 40px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
        }
        button {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-success { background: #10b981; color: white; }
        .btn-error { background: #ef4444; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-info { background: #3b82f6; color: white; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Smart Alerts Test</h1>
        <p>Click the buttons below to test the smart alerts:</p>
        
        <button class="btn-success" onclick="showSuccess()">Success Alert</button>
        <button class="btn-error" onclick="showError()">Error Alert</button>
        <button class="btn-warning" onclick="showWarning()">Warning Alert</button>
        <button class="btn-info" onclick="showInfo()">Info Alert</button>
        
        <br><br>
        <button onclick="testOldAlert()">Test Old Alert Override</button>
        <button onclick="testMultiple()">Test Multiple Alerts</button>
    </div>

    <script>
        // Simple Smart Alerts implementation
        class SmartAlerts {
            constructor() {
                this.container = null;
                this.alerts = new Map();
                this.alertCounter = 0;
                this.init();
            }

            init() {
                if (!document.querySelector('.smart-alert-container')) {
                    this.container = document.createElement('div');
                    this.container.className = 'smart-alert-container';
                    document.body.appendChild(this.container);
                } else {
                    this.container = document.querySelector('.smart-alert-container');
                }
            }

            show(message, type = 'info', title = '', duration = 5000) {
                const alertId = ++this.alertCounter;
                
                const alertElement = this.createAlertElement(alertId, message, type, title);
                this.container.appendChild(alertElement);
                
                this.alerts.set(alertId, {
                    element: alertElement,
                    timer: null
                });

                setTimeout(() => {
                    alertElement.classList.add('show');
                }, 10);

                if (duration > 0) {
                    this.startAutoHide(alertId, duration);
                }

                return alertId;
            }

            createAlertElement(id, message, type, title) {
                const alert = document.createElement('div');
                alert.className = `smart-alert ${type}`;
                alert.dataset.alertId = id;

                const { icon, defaultTitle } = this.getAlertConfig(type);
                const alertTitle = title || defaultTitle;

                alert.innerHTML = `
                    <div class="smart-alert-icon">${icon}</div>
                    <div class="smart-alert-content">
                        <div class="smart-alert-title">${alertTitle}</div>
                        <div class="smart-alert-message">${message}</div>
                    </div>
                    <button class="smart-alert-close" aria-label="Close">&times;</button>
                    <div class="smart-alert-progress" style="width: 100%"></div>
                `;

                const closeBtn = alert.querySelector('.smart-alert-close');
                closeBtn.addEventListener('click', () => this.hide(id));

                return alert;
            }

            getAlertConfig(type) {
                const configs = {
                    success: { icon: '✓', defaultTitle: 'Success' },
                    error: { icon: '✕', defaultTitle: 'Error' },
                    warning: { icon: '⚠', defaultTitle: 'Warning' },
                    info: { icon: 'i', defaultTitle: 'Information' }
                };
                return configs[type] || configs.info;
            }

            startAutoHide(alertId, duration) {
                const alertData = this.alerts.get(alertId);
                if (!alertData) return;

                const progressBar = alertData.element.querySelector('.smart-alert-progress');
                
                if (progressBar) {
                    progressBar.style.transition = `width ${duration}ms linear`;
                    progressBar.style.width = '0%';
                }

                alertData.timer = setTimeout(() => {
                    this.hide(alertId);
                }, duration);
            }

            hide(alertId) {
                const alertData = this.alerts.get(alertId);
                if (!alertData) return;

                if (alertData.timer) {
                    clearTimeout(alertData.timer);
                }

                alertData.element.classList.add('hide');

                setTimeout(() => {
                    if (alertData.element.parentNode) {
                        alertData.element.parentNode.removeChild(alertData.element);
                    }
                    this.alerts.delete(alertId);
                }, 300);
            }

            success(message, title = '') {
                return this.show(message, 'success', title, 4000);
            }

            error(message, title = '') {
                return this.show(message, 'error', title, 6000);
            }

            warning(message, title = '') {
                return this.show(message, 'warning', title, 5000);
            }

            info(message, title = '') {
                return this.show(message, 'info', title, 5000);
            }
        }

        // Create global instance
        window.SmartAlerts = new SmartAlerts();

        // Override alert
        window.originalAlert = window.alert;
        window.alert = function(message) {
            let type = 'info';
            let title = '';
            
            if (message.toLowerCase().includes('error') || message.toLowerCase().includes('failed')) {
                type = 'error';
                title = 'Error';
            } else if (message.toLowerCase().includes('success') || message.toLowerCase().includes('completed')) {
                type = 'success';
                title = 'Success';
            } else if (message.toLowerCase().includes('please') || message.toLowerCase().includes('warning')) {
                type = 'warning';
                title = 'Warning';
            }
            
            window.SmartAlerts.show(message, type, title);
        };

        // Test functions
        function showSuccess() {
            SmartAlerts.success('Operation completed successfully!', 'Success');
        }

        function showError() {
            SmartAlerts.error('Something went wrong. Please try again.', 'Error');
        }

        function showWarning() {
            SmartAlerts.warning('Please fill in all required fields.', 'Warning');
        }

        function showInfo() {
            SmartAlerts.info('Here is some helpful information.', 'Information');
        }

        function testOldAlert() {
            alert('This should show as a smart alert!');
        }

        function testMultiple() {
            SmartAlerts.info('First alert');
            setTimeout(() => SmartAlerts.success('Second alert'), 500);
            setTimeout(() => SmartAlerts.warning('Third alert'), 1000);
        }

        console.log('Smart Alerts loaded successfully!');
    </script>
</body>
</html>
