<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Smart Alerts</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 40px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .btn-success { background: #10b981; color: white; }
        .btn-error { background: #ef4444; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-info { background: #3b82f6; color: white; }
        .btn-old { background: #6b7280; color: white; }
        
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        h1 { color: #1f2937; margin-bottom: 10px; }
        h3 { color: #374151; margin-bottom: 15px; }
        p { color: #6b7280; margin-bottom: 20px; }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        .status.loading { background: #fef3c7; color: #92400e; }
        .status.ready { background: #d1fae5; color: #065f46; }
        .status.error { background: #fee2e2; color: #991b1b; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 Smart Alerts Test</h1>
        <p>Testing the new smart floating alert system</p>
        
        <div id="status" class="status loading">Loading Smart Alerts...</div>
        
        <div class="section">
            <h3>Basic Alert Types</h3>
            <button class="btn-success" onclick="testSuccess()">✓ Success Alert</button>
            <button class="btn-error" onclick="testError()">✕ Error Alert</button>
            <button class="btn-warning" onclick="testWarning()">⚠ Warning Alert</button>
            <button class="btn-info" onclick="testInfo()">ℹ Info Alert</button>
        </div>
        
        <div class="section">
            <h3>Real-world Examples</h3>
            <button class="btn-success" onclick="testModuleReset()">Module Reset Success</button>
            <button class="btn-warning" onclick="testFeedbackRequired()">Feedback Required</button>
            <button class="btn-error" onclick="testNetworkError()">Network Error</button>
            <button class="btn-info" onclick="testNotificationUpdate()">Notification Update</button>
        </div>
        
        <div class="section">
            <h3>Alert Override Test</h3>
            <button class="btn-old" onclick="testOldAlert()">Test Old alert() Function</button>
            <button class="btn-info" onclick="testMultiple()">Multiple Alerts</button>
        </div>
        
        <div class="section">
            <h3>Form Validation Examples</h3>
            <button class="btn-warning" onclick="testFormValidation()">Form Validation</button>
            <button class="btn-error" onclick="testUploadError()">Upload Error</button>
            <button class="btn-success" onclick="testEnrollmentSuccess()">Enrollment Success</button>
        </div>
    </div>

    <script src="assets/js/smart-alerts-simple.js"></script>
    <script>
        // Wait for SmartAlerts to be ready
        function checkSmartAlerts() {
            if (window.SmartAlerts) {
                document.getElementById('status').className = 'status ready';
                document.getElementById('status').textContent = '✓ Smart Alerts Ready!';
                console.log('SmartAlerts is available:', window.SmartAlerts);
            } else {
                document.getElementById('status').className = 'status error';
                document.getElementById('status').textContent = '✗ Smart Alerts Failed to Load';
                console.error('SmartAlerts not found');
            }
        }
        
        // Check after a short delay
        setTimeout(checkSmartAlerts, 500);
        
        // Test functions
        function testSuccess() {
            if (window.SmartAlerts) {
                SmartAlerts.success('Operation completed successfully!', 'Success');
            } else {
                alert('SmartAlerts not loaded');
            }
        }
        
        function testError() {
            if (window.SmartAlerts) {
                SmartAlerts.error('Something went wrong. Please try again.', 'Error');
            } else {
                alert('SmartAlerts not loaded');
            }
        }
        
        function testWarning() {
            if (window.SmartAlerts) {
                SmartAlerts.warning('Please fill in all required fields.', 'Warning');
            } else {
                alert('SmartAlerts not loaded');
            }
        }
        
        function testInfo() {
            if (window.SmartAlerts) {
                SmartAlerts.info('Here is some helpful information.', 'Information');
            } else {
                alert('SmartAlerts not loaded');
            }
        }
        
        function testModuleReset() {
            if (window.SmartAlerts) {
                SmartAlerts.success('Module has been reset successfully.', 'Module Reset');
            }
        }
        
        function testFeedbackRequired() {
            if (window.SmartAlerts) {
                SmartAlerts.warning('Please provide feedback before retaking the module.', 'Feedback Required');
            }
        }
        
        function testNetworkError() {
            if (window.SmartAlerts) {
                SmartAlerts.error('A network error occurred. Please check your connection and try again.', 'Network Error');
            }
        }
        
        function testNotificationUpdate() {
            if (window.SmartAlerts) {
                SmartAlerts.info('Notification has been marked as read.', 'Notification Updated');
            }
        }
        
        function testOldAlert() {
            alert('This should show as a smart alert instead of browser alert!');
        }
        
        function testMultiple() {
            if (window.SmartAlerts) {
                SmartAlerts.info('First alert');
                setTimeout(() => SmartAlerts.success('Second alert'), 500);
                setTimeout(() => SmartAlerts.warning('Third alert'), 1000);
                setTimeout(() => SmartAlerts.error('Fourth alert'), 1500);
            }
        }
        
        function testFormValidation() {
            if (window.SmartAlerts) {
                SmartAlerts.warning('Please select at least one recipient before sending the message.', 'Recipients Required');
            }
        }
        
        function testUploadError() {
            if (window.SmartAlerts) {
                SmartAlerts.error('An error occurred while uploading: File too large', 'Upload Failed');
            }
        }
        
        function testEnrollmentSuccess() {
            if (window.SmartAlerts) {
                SmartAlerts.success('Successfully enrolled in the course!', 'Enrollment Complete');
            }
        }
    </script>
</body>
</html>
