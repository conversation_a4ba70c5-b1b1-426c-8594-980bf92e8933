   <!DOCTYPE html>
   <html lang="en">

   <head>
      <meta charset="UTF-8">
      <meta name="description" content="About Brigada New Employee Onboarding (NEO) Program: This website helps employees improve by offering courses to upgrade their skills and knowledge about the company and specific competencies.">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Brigada Learning System</title>
      <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/boxicons@latest/css/boxicons.min.css">
      <link rel="icon" href="<?= base_url('favicon.ico'); ?>" type="image/x-icon">

      <link rel="preconnect" href="https://fonts.googleapis.com">
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
      <link href="https://fonts.googleapis.com/css2?family=Abril+Fatface&family=Anton&family=DM+Serif+Display:ital@0;1&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Playfair+Display:ital,wght@0,400..900;1,400..900&display=swap" rel="stylesheet">

      <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
      <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>

      <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>

      <!-- Ionicons -->
      <script type="module" src="https://cdn.jsdelivr.net/npm/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
      <script nomodule src="https://cdn.jsdelivr.net/npm/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>

      <!-- Smart Alerts System -->
      <script src="<?= base_url('assets/js/smart-alerts-simple.js') ?>"></script>

      <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

      <script>
         // Prevent HTML input in text inputs and textareas
         document.addEventListener('DOMContentLoaded', function() {
            const textInputs = document.querySelectorAll('input[type="text"], textarea');
            textInputs.forEach(function(input) {
               input.addEventListener('input', function() {
                  this.value = this.value.replace(/<\/?[^>]*>/g, '');
               });
            });
         });
      </script>

      <script>
         // Disable common screenshot keyboard shortcuts
         document.addEventListener('keydown', function(e) {
            // Prevents PrtScn (Print Screen) key
            if (e.key === 'PrintScreen') {
               alert('Screenshots are disabled on this page.');
               e.preventDefault();
            }

            // Prevents Shift + Win + S (Windows Snipping Tool)
            if (e.shiftKey && e.key === 'S' && (e.metaKey || e.ctrlKey)) {
               alert('Screenshots are disabled on this page.');
               e.preventDefault();
            }

            // Prevents Ctrl + S (Save Page) and Ctrl + P (Print Page)
            if ((e.ctrlKey && e.key === 's') || (e.ctrlKey && e.key === 'p')) {

               e.preventDefault();
            }
         });
      </script>

      <script>
         // Function to disable right-click on images and videos
         function disableRightClickOnMedia() {
            // Select all images and videos on the page
            const mediaElements = document.querySelectorAll('img, video');

            // Add event listener to prevent context menu on right-click
            mediaElements.forEach(media => {
               media.addEventListener('contextmenu', function(event) {
                  event.preventDefault(); // Prevent the context menu
               });
            });
         }

         // Call the function after the DOM content is fully loaded
         document.addEventListener('DOMContentLoaded', disableRightClickOnMedia);
      </script>


      <script>
         // Function to add "Draft Essay" and "Load Drafts" buttons below each textarea
         const addButtonsToTextareas = () => {
            const textareas = document.querySelectorAll('textarea');

            textareas.forEach((textarea, index) => {
               // Check if the buttons already exist to avoid duplicates
               const reflectiveContentLimit = textarea.closest('.essay-input')?.querySelector('.reflective-content-limit');
               if (reflectiveContentLimit && !reflectiveContentLimit.querySelector('.button-container')) {
                  // Create a container for the buttons
                  const buttonContainer = document.createElement('div');
                  buttonContainer.classList.add('button-container');

                  // Create the "Draft Essay" button
                  const draftButton = document.createElement('button');
                  draftButton.textContent = 'Save as Draft';
                  draftButton.classList.add('draft-button');

                  // Add a click event to save the text to localStorage
                  draftButton.addEventListener('click', () => {
                     const key = `textarea-${index}`;
                     const value = textarea.value.trim();
                     if (value) {
                        localStorage.setItem(key, value);
                        pasteButton.disabled = false; // Enable the "Load Drafts" button

                        // Change button text to "Draft Saved" for 2 seconds
                        const originalText = draftButton.textContent;
                        const originalBackgroundColor = draftButton.style.backgroundColor;
                        const originalBorderColor = draftButton.style.borderColor;

                        draftButton.innerHTML = '<p>Draft Saved</span></p>';
                        draftButton.style.backgroundColor = '#4CAF50'; // Change background color to green
                        draftButton.style.borderColor = '#4CAF50';

                        setTimeout(() => {
                           draftButton.textContent = originalText;
                           draftButton.style.backgroundColor = originalBackgroundColor; // Revert background color
                           draftButton.style.borderColor = originalBorderColor; // Revert border color
                        }, 2000);
                     } else {
                        alert('Cannot save an empty draft!');
                     }
                  });

                  // Create the "Load Drafts" button
                  const pasteButton = document.createElement('button');
                  pasteButton.textContent = 'Load Draft';
                  pasteButton.classList.add('paste-button');

                  // Disable the "Load Drafts" button if no draft exists
                  const key = `textarea-${index}`;
                  if (!localStorage.getItem(key)) {
                     pasteButton.disabled = true;
                  }

                  // Add a click event to retrieve the text from localStorage
                  pasteButton.addEventListener('click', () => {
                     const savedValue = localStorage.getItem(key);
                     if (savedValue !== null) {
                        textarea.value = savedValue;
                        SmartAlerts.success('Draft restored successfully!', 'Draft Loaded');
                     } else {
                        SmartAlerts.info('No draft found for this field.', 'No Draft Available');
                     }
                  });

                  // Append the buttons to the container
                  buttonContainer.appendChild(draftButton);
                  buttonContainer.appendChild(pasteButton);

                  // Append the button container inside the reflective-content-limit div
                  reflectiveContentLimit.appendChild(buttonContainer);
               }
            });
         };

         // Use MutationObserver to detect dynamically added textareas
         const observeDynamicTextareas = () => {
            const observer = new MutationObserver(() => {
               addButtonsToTextareas();
            });

            observer.observe(document.body, {
               childList: true,
               subtree: true,
            });
         };

         // Initialize on page load
         window.addEventListener('DOMContentLoaded', () => {
            addButtonsToTextareas(); // Add buttons for existing textareas
            observeDynamicTextareas(); // Watch for dynamically added textareas
         });
      </script>

      <script>
         function escapeHtml(text) {
            return text
               .replace(/&/g, "&amp;")
               .replace(/</g, "&lt;")
               .replace(/>/g, "&gt;")
               .replace(/"/g, "&quot;")
               .replace(/'/g, "&#039;");
         }

         function formatText(text) {
            return escapeHtml(text).replace(/\n/g, '<br>');
         }
      </script>

      <link rel="stylesheet" href="<?php echo base_url('css/notifications.css'); ?>" media="all">
      <link rel="stylesheet" href="<?php echo base_url('css/certificate_view.css'); ?>" media="all">
      <link rel="stylesheet" href="<?php echo base_url('css/completed.css'); ?>">
      <link rel="stylesheet" href="<?php echo base_url('css/result.css'); ?>">
      <link rel="stylesheet" href="<?php echo base_url('css/dashboard.css'); ?>">
      <link rel="stylesheet" href="<?php echo base_url('css/resume.css'); ?>">
      <link rel="stylesheet" href="<?php echo base_url('css/certificate.css'); ?>">
      <link rel="stylesheet" href="<?php echo base_url('css/header.css'); ?>">
      <link rel="stylesheet" href="<?php echo base_url('css/home.css'); ?>">
      <link rel="stylesheet" href="<?php echo base_url('css/courses.css'); ?>">
      <link rel="stylesheet" href="<?php echo base_url('css/course_detail.css'); ?>">
      <link rel="stylesheet" href="<?php echo base_url('css/footer.css'); ?>">

   </head>

   <body id="search-body">

      <div class="header-margin"></div>

      <header>

         <div class="small-container">
            <div class="header-item">
               <a class="img-base-url" href="<?php echo base_url('home'); ?>"><img src="<?php echo base_url('assets/icons/bls-logo.webp'); ?>" alt="Description of the image" loading="lazy"></a>
               <div class="header-nav-item"><a href="<?php echo base_url('home'); ?>">Home</a></div>
               <div class="courseBtn  header-nav-item"><a href="<?php echo base_url('courses'); ?>">Modules</a></div>
               <div class="learningBtn  header-nav-item"><a href="<?php echo base_url('resume'); ?>">Resume Learning</a></div>
               <div class="header-nav-item"><a href="<?php echo base_url('completed'); ?>">Completed</a></div>
            </div>

            <?php $isLoggedIn = $this->session->userdata('id') ? true : false; ?>
            <div class="header-item search-container">
               <form id="search-form" class="search-form" autocomplete="off">
                  <input
                     type="text"
                     name="query"
                     id="search-input"
                     placeholder="<?php echo $isLoggedIn ? 'Search Modules' : 'Log in to search for modules'; ?>"
                     required
                     <?php if (!$isLoggedIn) echo 'disabled'; ?>>
                  <button type="submit" <?php if (!$isLoggedIn) echo 'disabled'; ?>>
                     <ion-icon name="search-outline"></ion-icon>
                     Search
                  </button>
               </form>

               <!-- Dropdown panel for search results -->
               <div id="search-results-panel" class="search-results-panel" style="display: none;">
                  <h3>Modules</h3>
                  <ul id="search-results-list"></ul>
               </div>
            </div>

            <script>
               const BASE_URL = "<?= base_url(); ?>";
            </script>

            <script>
               document.addEventListener("DOMContentLoaded", function() {
                  const searchInput = document.getElementById("search-input");
                  const searchResultsPanel = document.getElementById("search-results-panel");
                  const searchResultsList = document.getElementById("search-results-list");

                  const modal = document.getElementById("customModal");
                  const modalCourseName = document.getElementById("modalCourseName");
                  const modalCourseDesignation = document.getElementById("modalCourseDesignation");
                  const modalCourseDescription = document.getElementById("modalCourseDescription");
                  const modalCourseImage = document.getElementById("modalCourseImage");
                  const confirmStartButton = document.getElementById("confirmStartButton");
                  const closeModalButton = document.getElementById("closeBtn-course");

                  searchInput.addEventListener("input", function() {
                     const query = searchInput.value.trim();

                     if (query.length > 0) {
                        fetch(BASE_URL + `courses/search?query=${encodeURIComponent(query)}`)
                           .then((response) => {
                              if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);
                              return response.json();
                           })
                           .then((courses) => {
                              searchResultsList.innerHTML = "";

                              if (courses.length > 0) {
                                 courses.forEach((course) => {
                                    const listItem = document.createElement("li");
                                    listItem.textContent = course.name;
                                    listItem.classList.add("search-result-item");

                                    // Attach data attributes for jQuery access
                                    listItem.setAttribute("data-id", course.id);
                                    listItem.setAttribute("data-name", course.name);
                                    listItem.setAttribute("data-description", course.description);
                                    listItem.setAttribute("data-designation", course.designation ?? "N/A");
                                    listItem.setAttribute("data-image", course.image_url ?? "");

                                    searchResultsList.appendChild(listItem);
                                 });
                              } else {
                                 const noResultsItem = document.createElement("li");
                                 noResultsItem.textContent = "No courses found.";
                                 searchResultsList.appendChild(noResultsItem);
                              }

                              searchResultsPanel.style.display = "block";
                              searchResultsPanel.classList.remove("hidden");

                           })
                           .catch((error) => {
                              console.error("Search fetch error:", error);
                              searchResultsList.innerHTML = "";
                              searchResultsPanel.style.display = "none";
                           });
                     } else {
                        searchResultsPanel.style.display = "none";
                     }
                  });

                  // Hide dropdown when clicking outside
                  document.addEventListener("click", function(event) {
                     if (!searchResultsPanel.contains(event.target) && event.target !== searchInput) {
                        searchResultsPanel.style.display = "none";
                     }
                  });

                  // Close modal
                  closeModalButton.addEventListener("click", function() {
                     modal.style.display = "none";
                  });

                  document.querySelector(".cancel-btn").addEventListener("click", function() {
                     modal.style.display = "none";
                  });

                  // Redirect to course view page
                  confirmStartButton.addEventListener("click", function() {
                     const moduleId = this.dataset.moduleId;
                     if (moduleId) {
                        window.location.href = BASE_URL + `courses/view/${moduleId}`;
                     }
                  });
               });

               // ✅ jQuery click handler (delegated)
               $(document).on("click", ".search-result-item", function(event) {
                  event.stopPropagation();

                  const name = $(this).data("name");
                  const designation = $(this).data("designation") || "N/A";
                  const description = $(this).data("description");
                  const image = $(this).data("image")?.trim() || BASE_URL + "assets/images/library.webp";
                  const id = $(this).data("id");

                  $("#modalCourseName").text(name);
                  $("#modalCourseDesignation").text(designation);
                  $("#modalCourseDescription").text(description);
                  $("#modalCourseImage").attr("src", image);

                  $("#confirmStartButton").attr("data-module-id", id);
                  $("#customModal").show();

                  $("#search-results-panel").hide().addClass("hidden");
                  $("#search-input").val("");

                  console.log("✅ Modal opened for:", name);
               });
            </script>



            <script>
               $(document).ready(function() {
                  var enrolledCourses = {}; // Track enrolled courses
                  var selectedCourseId = null;
                  var defaultImage = BASE_URL + "assets/images/library.webp";

                  var userID = <?php echo json_encode($this->session->userdata('id')); ?>;

                  // Show modal when any course card button is clicked
                  $(document).on('click', '.start-course-button', function(event) {
                     event.preventDefault();

                     selectedCourseId = $(this).data('course-id');
                     var courseName = $(this).data('course-name');
                     var courseDescription = $(this).data('course-description');
                     var courseDesignation = $(this).data('course-designation');
                     var courseImage = $(this).data('course-image');

                     if (!courseImage || courseImage.trim() === '') {
                        courseImage = defaultImage;
                     }

                     $('#modalCourseName').text(courseName);
                     $('#modalCourseDescription').text(courseDescription);
                     $('#modalCourseDesignation').text(courseDesignation);
                     $('#modalCourseImage').attr('src', courseImage);

                     $('#customModal').show();

                  });

                  // Show modal from search result
                  $(document).on('click', '.search-result-item', function() {
                     selectedCourseId = $(this).data('id');
                     var courseName = $(this).data('name');
                     var courseDescription = $(this).data('description');
                     var courseDesignation = $(this).data('designation');
                     var courseImage = $(this).data('image');

                     if (!courseImage || courseImage.trim() === '') {
                        courseImage = defaultImage;
                     }

                     $('#modalCourseName').text(courseName);
                     $('#modalCourseDescription').text(courseDescription);
                     $('#modalCourseDesignation').text(courseDesignation);
                     $('#modalCourseImage').attr('src', courseImage);
                     $('#customModal').show();
                  });

                  // Close modal
                  $('#closeBtn-course, .cancel-btn').on('click', function() {
                     $('#customModal').hide();
                  });

                  // Confirm start module
                  $('#confirmStartButton').on('click', function() {
                     if (!selectedCourseId) {
                        console.error("Missing course ID.");
                        return;
                     }

                     if (enrolledCourses[selectedCourseId]) {
                        return;
                     }

                     enrolledCourses[selectedCourseId] = true;

                     $.ajax({
                        url: '<?php echo site_url('courses/enroll'); ?>',
                        type: 'POST',
                        data: {
                           user_id: userID,
                           module_id: selectedCourseId,
                           course_status: 1
                        },
                        dataType: 'json',
                        success: function(response) {
                           if (response.status === 'success') {
                              window.location.href = "<?php echo site_url('courses/view/'); ?>" + selectedCourseId;
                           } else if (response.status === 'error') {
                              alert(response.message);
                           }
                        },
                        error: function(xhr, status, error) {
                           console.error('AJAX Error:', error);
                           console.error('Response:', xhr.responseText);
                        }
                     });

                     $('#customModal').hide();
                  });

                  // Real-time search
                  $('#search-input').on('input', function() {
                     const query = $(this).val().trim();
                     const $resultsPanel = $('#search-results-panel');
                     const $resultsList = $('#search-results-list');

                     if (query.length > 0) {
                        $.getJSON(BASE_URL + `courses/search`, {
                           query: query
                        }, function(courses) {
                           $resultsList.empty();

                           if (courses.length > 0) {
                              courses.forEach(course => {
                                 const $item = $('<li>')
                                    .addClass('search-result-item')
                                    .text(course.name)
                                    .data({
                                       id: course.id,
                                       name: course.name,
                                       description: course.description,
                                       designation: course.designation ?? 'N/A',
                                       image: course.image_url ?? ''
                                    });

                                 $resultsList.append($item);
                              });
                           } else {
                              $resultsList.append('<li>No courses found.</li>');
                           }

                           $resultsPanel.show();
                        }).fail(function(xhr) {
                           console.error('Search failed:', xhr.responseText);
                           $resultsList.empty();
                           $resultsPanel.hide();
                        });
                     } else {
                        $resultsPanel.hide();
                     }
                  });

                  // Hide search results panel when clicking outside
                  $(document).on('click', function(e) {
                     if (!$(e.target).closest('#search-results-panel, #search-input').length) {
                        $('#search-results-panel').hide();
                     }
                  });
               });
            </script>





            <div class="header-item">
               <ul>
                  <?php
                  $first_name = $this->session->userdata('first_name');
                  $last_name = $this->session->userdata('last_name');
                  $email = $this->session->userdata('email');
                  $department = $this->session->userdata('department');
                  $role = $this->session->userdata('role');
                  $user_id = $this->session->userdata('id');
                  ?>

                  <?php if ($first_name && $last_name && $email && $role && $user_id): ?>

                     <?php
                     // Extract the initials
                     $first_initial = strtoupper(substr($first_name, 0, 1));
                     $last_initial = strtoupper(substr($last_name, 0, 1));
                     ?>



                     <?php
                     $notification_count = $this->session->userdata('notification_count');
                     ?>

                     <!-- Message Notification Icon -->
                     <li class="message-notify">
                        <button class="nav-button" aria-label="Message" id="message-icon">
                           <i class="bx"><ion-icon name="chatbubbles-outline"></ion-icon></i>
                           <span id="unread-count"></span>
                        </button>

                        <button class="nav-button" aria-label="Notification" id="notification-toggle">
                           <i class="bx"><ion-icon name="notifications-outline"></ion-icon></i>
                           <!-- Conditionally display the span based on notification count -->
                           <?php if (isset($notification_count) && $notification_count > 0): ?>
                              <span id="notification-count">
                                 <?php echo $notification_count; ?>
                              </span>
                           <?php endif; ?>
                        </button>
                     </li>

                     <div class="notification" style="display: none;"> <!-- Initially hidden -->
                        <div class="notification-header">
                           <p>Notifications</p>
                           <button class="close-notification">Hide Notifications</button> <!-- Button to close/hide the notification -->
                        </div>

                        <div class="notification-list">
                           <!-- Notification list will be populated dynamically here -->
                        </div>
                     </div>



                     <li class="accountBtn">
                        <button>
                           <h5 class="initials"><span><?php echo $first_initial . $last_initial; ?></span></h5>
                           <a href="<?php echo base_url('dashboard'); ?>">Dashboard</a>
                        </button>

                     </li>

                     <li class="logoutBtn">
                        <span><a href="<?php echo site_url('home/logout'); ?>">Logout</a></span>
                     </li>

                     <li class="menu">
                        <i class="bx bx-menu"></i>
                     </li>

                  <?php else: ?>

                     <li class="loginBtn">
                        <button id="loginBtn">
                           <span>Enter Credentials</span>
                        </button>
                     </li>

                     <li class="list-menu">
                        <button><i class="bx bx-menu"></i></button>
                     </li>
                  <?php endif; ?>
               </ul>
            </div>
         </div>
      </header>


      <div class="header-nav-mobile">
         <div class="small-container">
            <div class="header-nav-item">
               <a href="<?php echo base_url('home'); ?>"> <img src="<?php echo base_url('assets/icons/bls-logo.webp'); ?>" alt="Description of the image" loading="lazy"></a>
            </div>

            <div class="header-nav-item">
               <button class="mobile-menu">
                  <?php if ($this->session->userdata('id')): ?>
                     <ion-icon name="menu-outline"></ion-icon>
                  <?php else: ?>
                     <span>Enter Credentials</span>
                     <ion-icon name="log-in-outline"></ion-icon>
                  <?php endif; ?>
                  <ion-icon name="close-outline" style="display: none;"></ion-icon>
               </button>
            </div>
         </div>
      </div>



      <!-- Custom Modal HTML -->
      <div id="customModal" class="custom-modal active-modal">
         <div class="custom-modal-content">
            <div class="module-thumbnail">
               <!-- Make sure to give the img an id for JavaScript targeting -->
               <img id="modalCourseImage" src="" alt="Course Image">
            </div>
            <div class="module-content-confirm">
               <div class="module-content-title">
                  <h2 id="modalCourseName"></h2>
                  <p id="modalCourseDesignation"></p>
                  <p id="modalCourseDescription"></p>
               </div>

               <div class="module-content-button">
                  <button id="confirmStartButton" class="confirm-btn">Start Module now</button>
                  <button class="cancel-btn">Cancel</button>
               </div>
            </div>
         </div>
         <div class="closeBtn-course-container">
            <button id="closeBtn-course">Cancel and Exit Module</button>
         </div>
      </div>




      <div id="mobile-loginPanel">
         <div class="mobile-navigation">

            <?php if ($this->session->userdata('id')): ?>
               <div class="mobile-nav-item with-session">
                  <div class="small-container">
                     <div class="small-dashboard">
                        <h3>Dashboard</h3>
                        <div class="dashboard-menu">

                           <div class="dashboard-menu-item">
                              <div class="progress-dash">
                                 <div class="chart-wrapper">
                                    <div id="chart_div"></div>
                                 </div>
                              </div>
                              <p>In Progress Modules</p>
                           </div>

                           <div class="dashboard-menu-item">
                              <div class="progress-dash">
                                 <div class="chart-wrapper">
                                    <div id="chart_courses"></div>
                                 </div>
                              </div>
                              <p>Completed Modules</p>
                           </div>

                        </div>
                     </div>

                     <ul>
                        <li><ion-icon name="planet-outline"></ion-icon><a href="<?php echo base_url('home'); ?>">Home</a></li>
                        <li><ion-icon name="book-outline"></ion-icon><a href="<?php echo base_url('courses'); ?>">Modules</a></li>
                        <li><ion-icon name="alarm-outline"></ion-icon><a href="<?php echo base_url('resume'); ?>">Resume Learning</a></li>
                        <li><ion-icon name="trophy-outline"></ion-icon><a href="<?php echo base_url('completed'); ?>">Completed</a></li>
                        <li><ion-icon name="chatbubbles-outline"></ion-icon><a href="<?php echo base_url('messages'); ?>">Messages</a></li>
                        <li><ion-icon name="notifications-outline"></ion-icon><a href="<?php echo base_url('notifications'); ?>">Notifications</a></li>
                     </ul>

                     <div class="session-out">
                        <ion-icon name="log-out-outline"></ion-icon>
                        <a href="<?php echo site_url('home/logout'); ?>">Logout Employee</a>
                     </div>
                  </div>
               </div>

               <!-- Google Charts -->

               <script type="text/javascript">
                  google.charts.load('current', {
                     'packages': ['corechart']
                  });
                  google.charts.setOnLoadCallback(drawCharts);

                  function drawCharts() {
                     drawChart();
                     drawCoursesChart();
                  }

                  function drawChart() {
                     var data = new google.visualization.DataTable();
                     data.addColumn('string', 'Module Status');
                     data.addColumn('number', 'Count');
                     data.addRows([
                        ['Completed', 8],
                        ['Pending', 2]
                     ]);

                     var options = {
                        pieHole: 0.4,
                        legend: 'none',
                        pieSliceText: 'none',
                        tooltip: {
                           trigger: 'none'
                        },
                        chartArea: {
                           width: '80%',
                           height: '80%'
                        },
                        backgroundColor: 'transparent'
                     };

                     var chart = new google.visualization.PieChart(document.getElementById('chart_div'));
                     chart.draw(data, options);
                  }

                  function drawCoursesChart() {
                     var data = google.visualization.arrayToDataTable([
                        ['Status', 'Count'],
                        ['Completed', 60],
                        ['Remaining', 40]
                     ]);

                     var options = {
                        pieHole: 0.4,
                        legend: 'none',
                        pieSliceText: 'none',
                        tooltip: {
                           trigger: 'none'
                        },
                        chartArea: {
                           width: '80%',
                           height: '80%'
                        },
                        backgroundColor: 'transparent'
                     };

                     var chart = new google.visualization.PieChart(document.getElementById('chart_courses'));
                     chart.draw(data, options);
                  }
               </script>

            <?php else: ?>

               <div class="mobile-nav-item no-session">
                  <div class="small-container">

                     <div class="no-session-item">
                        <div class="mobile-intro">
                           <h5>Welcome to <span class="seri-style-medium">Brigada Learning System</span></h5>
                           <p>Enter your credentials to log in. If you need help, use the <a href="#">Recovery Link</a> or <a href="#">Contact Support</a>.</p>
                        </div>

                        <form id="mobile-login-form">
                           <div class="form-item">
                              <input type="email" name="email" placeholder="Email" required>
                           </div>
                           <div class="form-item password-input-container">
                              <input type="password" name="password" placeholder="Password" required autocomplete="password">
                              <button type="button" class="password-toggle-btn" onclick="togglePassword(this)">
                                 <ion-icon name="eye-outline" class="show-password"></ion-icon>
                                 <ion-icon name="eye-off-outline" class="hide-password" style="display: none;"></ion-icon>
                              </button>
                           </div>
                           <div class="form-item">
                              <button type="button" name="submit_login" class="ver-submit" onclick="submitForm('mobile-login-form')">Sign In</button>
                           </div>
                           <div class="form-item">
                              <div id="error-message"></div>
                           </div>
                        </form>
                     </div>

                     <div class="no-session-item">
                        <div class="form-item trouble-login">
                           <h4>Having trouble logging in?</h4>
                           <ol>
                              <li>Ensure you are using your registered company email and password.</li>
                              <li>If you are still unable to log in, visit the HR department or submit a support request through the internal help desk.</li>
                           </ol>
                        </div>
                     </div>

                  </div>
               </div>

            <?php endif; ?>
         </div>
      </div>



      <script>
         // Add an event listener to the mobile-menu button
         document.querySelector('.mobile-menu').addEventListener('click', function() {
            // Toggle the open class on the #mobile-loginPanel
            document.querySelector('#mobile-loginPanel').classList.toggle('open');

            // Toggle the icon
            var menuIcon = document.querySelector('.mobile-menu ion-icon[name="menu-outline"], .mobile-menu ion-icon[name="log-in-outline"]');
            var closeIcon = document.querySelector('.mobile-menu ion-icon[name="close-outline"]');
            if (document.querySelector('#mobile-loginPanel').classList.contains('open')) {
               menuIcon.style.display = 'none';
               closeIcon.style.display = 'block';
            } else {
               menuIcon.style.display = 'block';
               closeIcon.style.display = 'none';
            }
         });
      </script>


      <div id="loginPanel" class="panel">
         <button class="arrow-back" id="closeLogin">&#8594;</button>
         <div class="panel-input">


            <div class="session-panel">

               <div class="session-intro">
                  <h5>Welcome to Brigada Learning System</h5>
                  <p>Enter your credentials to log in. If you need help, use the <a href="#">Recovery Link</a> or <a href="#">Contact Support</a>.</p>
               </div>

               <form id="desktop-login-form">
                  <div class="form-item">
                     <input type="email" name="email" placeholder="Email" required>
                  </div>
                  <div class="form-item password-input-container">
                     <input type="password" name="password" placeholder="Password" required autocomplete="password">
                     <button type="button" class="password-toggle-btn" onclick="togglePassword(this)">
                        <ion-icon name="eye-outline" class="show-password"></ion-icon>
                        <ion-icon name="eye-off-outline" class="hide-password" style="display: none;"></ion-icon>
                     </button>
                  </div>
                  <div class="form-item">
                     <button type="button" name="submit_login" class="ver-submit" onclick="submitForm('desktop-login-form')">Sign In</button>
                  </div>
                  <div class="form-item">
                     <div id="error-message"></div>
                  </div>
                  <div class="form-item trouble-login">
                     <h4>Having trouble logging in?</h4>
                     <ol>
                        <li>Ensure you are using your registered company email and password.</li>
                        <li>If you are still unable to log in, visit the HR department or submit a support request through the internal help desk.</li>
                     </ol>
                  </div>
               </form>

            </div>





         </div>

         <div class="panel-ads">
            <img src="<?php echo base_url('assets/images/books-cred (1)-2.webp'); ?>" alt="Description of the image" width="100%" loading="lazy">
            <div class="direct-email"><i class='bx bx-envelope'></i><a href="mailto:<EMAIL>">Email us <NAME_EMAIL></a></div>
         </div>
      </div>



      <script>
         function submitForm(formId) {
            // Check if form exists
            if (!$('#' + formId).length) {
               console.error('Form with ID "' + formId + '" not found');
               return;
            }

            const emailInput = $('#' + formId + ' input[name="email"]');
            const passwordInput = $('#' + formId + ' input[name="password"]');
            const errorBox = $('#' + formId + ' #error-message');

            // Check if required elements exist
            if (!emailInput.length || !passwordInput.length || !errorBox.length) {
               console.error('Required form elements not found in form: ' + formId);
               return;
            }

            const email = (emailInput.val() || '').trim();
            const password = (passwordInput.val() || '').trim();

            // Clear previous state
            errorBox.html('');

            if (!email || !password) {
               errorBox.html('<p class="network-error-prompt">Please enter both email and password.</p>');
               return;
            }

            errorBox.html('<p class="login-user-prompt">Logging in, please wait...</p>');

            $.ajax({
               type: 'POST',
               url: '<?php echo site_url('home/login_submit'); ?>',
               data: {
                  email,
                  password
               },
               dataType: 'json',
               success: function(response) {
                  if (response.success) {
                     errorBox.html('<p class="login-user-prompt">Login successful! Redirecting...</p>');
                     setTimeout(() => {
                        window.location.href = '<?php echo site_url('home'); ?>';
                     }, 800);
                  } else {
                     errorBox.html('<p class="network-error-prompt">' + response.error + '</p>');
                  }
               },
               error: function() {
                  errorBox.html('<p class="network-error-prompt">Something went wrong. Please try again.</p>');
               }
            });
         }

         // Add event listeners for Enter key press on both forms
         document.addEventListener('DOMContentLoaded', function() {
            const desktopForm = document.getElementById('desktop-login-form');
            const mobileForm = document.getElementById('mobile-login-form');

            if (desktopForm) {
               desktopForm.addEventListener('keypress', function(event) {
                  if (event.key === 'Enter') {
                     event.preventDefault();
                     submitForm('desktop-login-form');
                  }
               });
            }

            if (mobileForm) {
               mobileForm.addEventListener('keypress', function(event) {
                  if (event.key === 'Enter') {
                     event.preventDefault();
                     submitForm('mobile-login-form');
                  }
               });
            }
         });
      </script>
      <script>
         // Password toggle functionality
         function togglePassword(button) {
            const passwordInput = button.parentElement.querySelector('input[type="password"], input[type="text"]');
            const showIcon = button.querySelector('.show-password');
            const hideIcon = button.querySelector('.hide-password');

            if (passwordInput.type === 'password') {
               passwordInput.type = 'text';
               showIcon.style.display = 'none';
               hideIcon.style.display = 'block';
            } else {
               passwordInput.type = 'password';
               showIcon.style.display = 'block';
               hideIcon.style.display = 'none';
            }
         }
      </script>





      <script>
         // Select all hide links
         const hideLinks = document.querySelectorAll('.hideLink');

         // Add click event listeners to all hide links
         hideLinks.forEach(link => {
            link.addEventListener('click', function(event) {
               event.preventDefault(); // Prevent default link behavior

               // Hide the closest alert div
               this.closest('.alert').style.display = 'none';
            });
         });
      </script>


      <script>
         document.addEventListener('DOMContentLoaded', function() {
            // Function to set up event listeners if elements exist
            function setupPanelToggling() {
               const loginBtn = document.getElementById('loginBtn');
               const loginPanel = document.getElementById('loginPanel');

               // Only set up event listeners if elements are present
               if (loginBtn && loginPanel) {
                  loginBtn.addEventListener('click', function() {
                     // Get the computed style for the login panel
                     const currentMarginRight = window.getComputedStyle(loginPanel).marginRight;

                     // Toggle between '0%' and '-100%' for the marginRight
                     loginPanel.style.marginRight = (currentMarginRight === '0px' || currentMarginRight === '0%') ? '-100%' : '0';
                  });
               }
            }
            // Initial setup
            setupPanelToggling();
         });
      </script>





      <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

      <script>
         $(document).ready(function() {
            // Close login panel
            $('#closeLogin').click(function() {
               $('#loginPanel').css('margin-right', '-100%');
            });

         });
      </script>

      <script>
         // Ensure the element with id 'role' exists before attaching event listener
         const roleElement = document.getElementById('role');
         if (roleElement) {
            roleElement.addEventListener('change', function() {
               var role = this.value;
               var adminRoleContainer = document.getElementById('admin-role-container');
               var employeeStatusContainer = document.getElementById('employee-status-container');

               console.log("Role selected: " + role); // Debugging log
               console.log(adminRoleContainer); // Debugging log
               console.log(employeeStatusContainer); // Debugging log

               if (role === 'admin') {
                  adminRoleContainer.classList.remove('hidden');
                  employeeStatusContainer.classList.add('hidden');
               } else {
                  adminRoleContainer.classList.add('hidden');
                  employeeStatusContainer.classList.remove('hidden');
               }
            });

            // Initialize visibility based on the default selection
            roleElement.dispatchEvent(new Event('change'));
         }
      </script>






















      <div class="message-box-container" style="display: none;">
         <h3>Messages</h3>

         <div class="scroll-message-container">
            <div class="search-bar">
               <input type="text" id="search-recipient" placeholder="Search for a recipient...">
            </div>

            <div class="friends-list">
               <h3>Department Admins</h3>
               <ul id="friends-list"></ul>
            </div>

            <div class="recent-messages">
               <h3>Recent Messages</h3>
               <ul id="recent-messages"></ul>
            </div>

            <button class="chat-messagebox" onclick="hideMessageBox()">Hide Message Box</button>
         </div>
      </div>


      <div class="chat-box" id="chat-box" style="display: none;">
         <div class="chatbox-header">
            <div class="icon-circle">
               <img id="chat-friend-image" src="" alt="Friend's Profile Image" width="35" height="35" style="border-radius: 50%; display: none;" loading="lazy">
            </div>
            <span id="chat-friend-name"></span>
            <div id="user-status"><span id="status-span"></span></div>
         </div>

         <div class="chat-content" id="chat-content"></div>

         <div class="attachment-container">
            <!-- File Input -->
            <input type="file" id="file-attachment-input" accept=".pdf,.doc,.docx,.txt" multiple style="display: none;">
            <!-- Image Input -->
            <input type="file" id="image-attachment-input" accept="image/*" multiple style="display: none;">

            <div class="cancel-remove-btn-container">
               <button id="add-more-btn" style="display: none;">
                  <ion-icon id="add-more-icon" name="add-outline"></ion-icon> Add More
               </button>
               <button id="cancel-btn" style="display: none;">Cancel</button>
            </div>
            <!-- Preview Section -->
            <div id="preview-container"></div>

         </div>

         <div class="chat-send">
            <div class="attachments">
               <!-- File Attachment Button -->
               <button id="file-attachment-btn">
                  <ion-icon name="link-outline"></ion-icon>
               </button>
               <!-- Image Attachment Button -->
               <button id="image-attachment-btn">
                  <ion-icon name="image-outline"></ion-icon>
               </button>
            </div>

            <textarea id="chat-input" placeholder="Type your message..."></textarea>
            <button onclick="sendMessage()">
               <ion-icon name="send-outline"></ion-icon>
            </button>
         </div>

         <button class="chat-hide" onclick="hideChat()">Hide Chat</button>
      </div>


      <script>
         document.addEventListener("DOMContentLoaded", function() {
            const previewContainer = document.getElementById("preview-container");

            let isDown = false; // Track if the mouse or touch is active
            let startX; // Starting X position
            let scrollLeft; // Initial scroll position

            // Prevent default drag behavior for all child elements
            previewContainer.addEventListener("dragstart", (e) => {
               e.preventDefault();
            });

            // Mouse down event
            previewContainer.addEventListener("mousedown", (e) => {
               isDown = true;
               previewContainer.classList.add("active");
               startX = e.pageX - previewContainer.offsetLeft; // Mouse position relative to container
               scrollLeft = previewContainer.scrollLeft; // Current scroll position
            });

            // Mouse leave event
            previewContainer.addEventListener("mouseleave", () => {
               isDown = false;
               previewContainer.classList.remove("active");
            });

            // Mouse up event
            previewContainer.addEventListener("mouseup", () => {
               isDown = false;
               previewContainer.classList.remove("active");
            });

            // Mouse move event
            previewContainer.addEventListener("mousemove", (e) => {
               if (!isDown) return; // Only execute if the mouse is down
               e.preventDefault(); // Prevent default behavior
               const x = e.pageX - previewContainer.offsetLeft; // Current mouse position
               const walk = (x - startX) * 2; // Scroll speed multiplier
               previewContainer.scrollLeft = scrollLeft - walk; // Update scroll position
            });

            // Touch support for mobile devices
            previewContainer.addEventListener("touchstart", (e) => {
               isDown = true;
               startX = e.touches[0].pageX - previewContainer.offsetLeft; // Touch start position
               scrollLeft = previewContainer.scrollLeft; // Current scroll position
            }, {
               passive: true
            }); // Mark listener as passive

            previewContainer.addEventListener("touchmove", (e) => {
               if (!isDown) return; // Only execute if the touch is active
               e.preventDefault(); // Prevent default behavior
               const x = e.touches[0].pageX - previewContainer.offsetLeft; // Current touch position
               const walk = (x - startX) * 2; // Scroll speed multiplier
               previewContainer.scrollLeft = scrollLeft - walk; // Update scroll position
            }, {
               passive: false
            }); // Passive false if you want to prevent default

            // Touch end event
            previewContainer.addEventListener("touchend", () => {
               isDown = false;
            });
         });
      </script>



      <script>
         document.addEventListener("DOMContentLoaded", function() {
            const fileAttachmentBtn = document.getElementById('file-attachment-btn');
            const imageAttachmentBtn = document.getElementById('image-attachment-btn');
            const fileAttachmentInput = document.getElementById('file-attachment-input');
            const imageAttachmentInput = document.getElementById('image-attachment-input');
            const previewContainer = document.getElementById('preview-container');
            const addMoreBtn = document.getElementById('add-more-btn');
            const cancelBtn = document.getElementById('cancel-btn');
            const addMoreIcon = document.getElementById('add-more-icon');

            let initialAttachmentType = null; // Track the initial attachment type

            // Disable one input while the other is active
            function toggleInputAvailability(disableFileInput, disableImageInput) {
               fileAttachmentBtn.disabled = disableFileInput;
               imageAttachmentBtn.disabled = disableImageInput;
            }

            // Function to create a preview for files and images
            function createPreview(file, type) {
               const previewWrapper = document.createElement('div');
               previewWrapper.classList.add('preview-wrapper');
               previewWrapper.style.display = 'flex';
               previewWrapper.style.alignItems = 'center';

               // Create remove button for each preview
               const removeBtn = document.createElement('button');
               removeBtn.innerHTML = '<ion-icon name="trash-outline"></ion-icon>';
               removeBtn.classList.add('remove-btn');

               // Display preview based on type
               if (type === 'file') {
                  const fileName = document.createElement('p');
                  fileName.textContent = file.name;
                  previewWrapper.appendChild(fileName);
               } else if (type === 'image') {
                  const imagePreview = document.createElement('img');
                  const reader = new FileReader();
                  reader.onload = function(e) {
                     imagePreview.src = e.target.result;
                     imagePreview.style.width = '50px';
                     imagePreview.style.height = '50px';
                     imagePreview.style.borderRadius = '4px';
                  };
                  reader.readAsDataURL(file);
                  previewWrapper.appendChild(imagePreview);
               }

               previewWrapper.appendChild(removeBtn); // Append the remove button
               previewContainer.appendChild(previewWrapper); // Append the preview wrapper

               // Handle the remove button click event
               removeBtn.addEventListener('click', function() {
                  previewWrapper.remove();
                  if (previewContainer.children.length === 0) {
                     addMoreBtn.style.display = 'none';
                     cancelBtn.style.display = 'none';
                  }
               });
            }

            // Handle file input selection
            fileAttachmentInput.addEventListener('change', function(e) {
               const files = e.target.files;
               if (files.length > 0) {
                  toggleInputAvailability(false, true);
                  initialAttachmentType = 'file';
                  addMoreIcon.setAttribute('name', 'document-outline');
                  addMoreBtn.style.display = 'block';
                  cancelBtn.style.display = 'block';
                  for (const file of files) {
                     createPreview(file, 'file');
                  }
               }
               fileAttachmentInput.value = '';
            });

            // Handle image input selection
            imageAttachmentInput.addEventListener('change', function(e) {
               const files = e.target.files;
               if (files.length > 0) {
                  toggleInputAvailability(true, false);
                  initialAttachmentType = 'image';
                  addMoreIcon.setAttribute('name', 'images-outline');
                  addMoreBtn.style.display = 'block';
                  cancelBtn.style.display = 'block';
                  for (const file of files) {
                     createPreview(file, 'image');
                  }
               }
               imageAttachmentInput.value = '';
            });

            // Add More button functionality
            addMoreBtn.addEventListener('click', function() {
               if (initialAttachmentType === 'file') {
                  fileAttachmentInput.click();
               } else if (initialAttachmentType === 'image') {
                  imageAttachmentInput.click();
               }
            });

            // Cancel button functionality
            cancelBtn.addEventListener('click', function() {
               previewContainer.innerHTML = '';
               toggleInputAvailability(false, false);
               initialAttachmentType = null;
               addMoreBtn.style.display = 'none';
               cancelBtn.style.display = 'none';
            });

            // Open file input
            fileAttachmentBtn.addEventListener('click', function() {
               fileAttachmentInput.click();
            });

            // Open image input
            imageAttachmentBtn.addEventListener('click', function() {
               imageAttachmentInput.click();
            });
         });

         function sendMessage() {
            const input = document.getElementById("chat-input");
            if (input.value.trim()) {
               console.log("Message sent:", input.value);
               input.value = "";
            }
         }

         function hideChat() {
            const chatBox = document.getElementById('chat-box');
            chatBox.style.display = 'none';
            console.log("Chat hidden");
         }
      </script>






      <script>
         // Function to hide the .chat-box div
         function hideChat() {
            const chatBox = document.querySelector('.chat-box');
            if (chatBox) {
               chatBox.style.display = 'none';
            }
         }

         // Function to hide the .message-box-container div
         function hideMessageBox() {
            const chatBox = document.querySelector('.chat-box');
            const messageBox = document.querySelector('.message-box-container');
            if (messageBox) {
               messageBox.style.display = 'none';
               chatBox.style.right = '5%';
            }
         }
      </script>

      <script>
         const textarea = document.querySelector('.chat-send textarea');

         textarea.addEventListener('input', function() {
            textarea.style.height = '30px';

            let newHeight = Math.min(textarea.scrollHeight, 120);
            textarea.style.height = newHeight + 'px';
            let radius = 36 - ((newHeight - 30) / 90) * (36 - 12);
            textarea.style.borderRadius = `${radius}px`;
         });
      </script>

      <script>
         const friendList = document.getElementById('friends-list');

         let isDown = false;
         let startX;
         let scrollLeft;

         friendList.addEventListener('mousedown', (e) => {
            isDown = true;
            friendList.classList.add('active');
            startX = e.pageX - friendList.offsetLeft;
            scrollLeft = friendList.scrollLeft;
         });

         friendList.addEventListener('mouseleave', () => {
            isDown = false;
            friendList.classList.remove('active');
         });

         friendList.addEventListener('mouseup', () => {
            isDown = false;
            friendList.classList.remove('active');
         });

         friendList.addEventListener('mousemove', (e) => {
            if (!isDown) return;
            e.preventDefault();
            const x = e.pageX - friendList.offsetLeft;
            const walk = (x - startX) * 2;
            friendList.scrollLeft = scrollLeft - walk;
         });
      </script>




      <script>
         $(document).ready(function() {

            var friendId = null;
            var userId = <?= json_encode($this->session->userdata('id')) ?>;


            if (userId === null) {
               $('#message-icon').hide();
               return;
            }

            $('#message-icon').on('click', function() {
               const chatBox = document.querySelector('.chat-box');
               chatBox.style.right = 'calc(380px + 5% + 24px)';
               $('.message-box-container').toggle();
               $('#unread-count').text('0');
               $('#unread-count').hide();
            });

            loadFriends();
            loadRecentConversations();
            getUnreadCount();

            $('#search-recipient').on('input', function() {
               const query = $(this).val().toLowerCase();

               $.ajax({
                  url: '<?= base_url("MessagesController/searchFriends") ?>',
                  type: 'GET',
                  data: {
                     query: query
                  },
                  success: function(data) {
                     displayFriends(JSON.parse(data));
                  }
               });
            });


            function loadRecentConversations() {
               $.ajax({
                  url: '<?= base_url("MessagesController/getRecentConversations") ?>',
                  type: 'GET',
                  success: function(data) {
                     let conversations = JSON.parse(data);
                     conversations.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                     displayRecentConversations(conversations);
                  },
                  error: function(xhr, status, error) {
                     console.error("Error loading recent conversations:", error);
                  }
               });
            }

            function displayRecentConversations(conversations) {
               $('#recent-messages').empty();
               let displayedFriends = new Set(); // To track unique friend IDs

               conversations.forEach(convo => {
                  // Determine the friend ID
                  let friendId = (convo.sender_id == userId) ? convo.recipient_user_id : convo.sender_id;

                  // Skip adding this friend if they are already in the list
                  if (displayedFriends.has(friendId)) {
                     return;
                  }
                  displayedFriends.add(friendId); // Track this friend as displayed

                  let imageUrl = convo.image_url ? convo.image_url : '<?= base_url("assets/icons/user_placeholder.webp") ?>';
                  let timeAgo = getRelativeTime(convo.created_at);

                  let messageText = convo.message;
                  if (convo.deleted_by_sender == 1) {
                     messageText = 'Message deleted';
                  }

                  $('#recent-messages').append(`
                     <li onclick="selectFriend(${friendId}, '${convo.first_name}', '${imageUrl}')">
                           <span class='img-circle-span'>
                              <img src="${imageUrl}" alt="Profile Image" width="35" height="35" loading="lazy">
                           </span>
                           <div class='span-text-container'>
                              <span class='span-text'>${convo.first_name}</span>
                              <span class='span-message'>${messageText.substring(0, 24)}...</span>
                           </div>
                           <span class='span-time'>${timeAgo}</span>
                     </li>
                  `);
               });
            }


            function loadFriends() {
               $.ajax({
                  url: '<?= base_url("MessagesController/getFriends") ?>',
                  type: 'GET',
                  success: function(data) {
                     displayFriends(JSON.parse(data));
                  }
               });
            }

            function displayFriends(friends) {
               $('#friends-list').empty();
               friends.forEach(friend => {
                  let imageUrl = friend.image_url ? friend.image_url.replace('s3://', 'https://YOUR_BUCKET_NAME.s3.amazonaws.com/') : '<?= base_url("assets/icons/user_placeholder.webp") ?>';
                  $('#friends-list').append(`<li onclick="selectFriend(${friend.id}, '${friend.first_name}', '${imageUrl}')">
                     <span class='circle-span'><img src="${imageUrl}" alt="Profile Image" width="35" height="35" loading="lazy"></span>
                     <span class='span-text'>${friend.first_name}</span></li>`);
               });
            }

            window.selectFriend = function(selectedFriendId, friendName, friendImageUrl) {
               friendId = selectedFriendId;

               $('#chat-friend-name').text(friendName).data('friend-id', selectedFriendId);
               $('#chat-friend-image').attr('src', friendImageUrl).show();
               $('#chat-box').show();

               loadChatMessages(friendId);

               $.ajax({
                  url: '<?= base_url("MessagesController/markMessagesAsRead") ?>',
                  type: 'POST',
                  data: {
                     friend_id: friendId
                  },
                  success: function(data) {
                     console.log('Messages marked as read for friend ID:', friendId);
                  },
                  error: function(xhr, status, error) {
                     console.error('Error marking messages as read:', error);
                  }
               });

               startMessagePolling(friendId);
               startStatusInterval();
            };


            function loadChatMessages(friendId) {
               $.ajax({
                  url: '<?= base_url("MessagesController/getMessagesWithFriend") ?>',
                  type: 'POST',
                  data: {
                     friend_id: friendId
                  },
                  success: function(response) {
                     let messages = JSON.parse(response);
                     displayChatMessages(messages);
                  },
                  error: function(xhr, status, error) {
                     console.error('Error loading chat messages:', error);
                  }
               });
            }


            // Add event listener for delete message button
            // Add event listener for delete message button
            $(document).on('click', '.delete-message-button', function() {
               const messageId = $(this).data('message-id');
               // Send AJAX request to update 'deleted_by_sender' column in messages table
               $.ajax({
                  url: '<?= base_url("MessagesController/updateMessage") ?>',
                  type: 'POST',
                  data: {
                     message_id: messageId,
                     deleted_by_sender: 1
                  },
                  success: function(response) {
                     console.log('Message deleted successfully');
                     // Update UI to reflect deleted message
                     $(`[data-message-id="${messageId}"]`).closest('.message').remove();
                     // Load updated chat messages
                     loadChatMessages(friendId);
                  },
                  error: function(xhr, status, error) {
                     console.error('Error deleting message:', error);
                  }
               });
            });



            function displayChatMessages(messages) {
               $('#chat-content').empty();

               messages.forEach(message => {

                  function initializeUser() {
                     // Check if session data exists
                     <?php if ($this->session->userdata('id')): ?>
                        const userId = <?= json_encode($this->session->userdata('id')) ?>; // Use json_encode to ensure proper formatting for JavaScript
                        console.log("User ID:", userId);
                        // Proceed with further logic
                     <?php else: ?>
                        console.log("No active session found.");
                     <?php endif; ?>
                  }

                  const messageClass = message.sender_id == userId ? 'sent' : 'received';
                  const messageId = message.id;
                  const messageTime = getRelativeTime(message.created_at);

                  let messageTextHtml = '';
                  if (message.deleted_by_sender == 1) {
                     messageTextHtml = '<div class="message-text message-deleted">Message deleted</div>';
                  } else {
                     messageTextHtml = '<div class="message-text">' + message.message + '</div>';
                     if (messageClass === 'sent') {
                        messageTextHtml += '<button class="delete-message-button" data-message-id="' + messageId + '">Remove</button>';
                     }
                  }

                  $('#chat-content').append(`
               <div class="${messageClass}">
                  ${messageTextHtml}
                  <span class="message-time">${messageTime}</span>
               </div>
         `);
               });

               adjustBorderRadius();

               // Optionally, scroll the chat box to the bottom when new messages are added
               $('#chat-content').scrollTop($('#chat-content')[0].scrollHeight);
            }



            function adjustBorderRadius() {

               const messages = document.querySelectorAll('.received .message-text, .sent .message-text');

               messages.forEach(message => {
                  const height = message.offsetHeight;

                  if (height > 30) {
                     message.parentElement.classList.add('multi-line');
                  } else {
                     message.parentElement.classList.remove('multi-line');
                  }
               });
            }




            let messagePollingInterval;

            function startMessagePolling(friendId) {
               if (messagePollingInterval) {
                  clearInterval(messagePollingInterval);
               }

               messagePollingInterval = setInterval(function() {
                  $.ajax({
                     url: '<?= base_url("MessagesController/getMessagesWithFriend") ?>',
                     type: 'POST',
                     data: {
                        friend_id: friendId
                     },
                     success: function(response) {
                        let messages = JSON.parse(response);
                        const latestMessage = messages[messages.length - 1];
                        const lastMessageTime = new Date(latestMessage.created_at).getTime();
                        const lastSeenTime = $('#chat-content').data('last-seen-time') || 0;

                        if (lastMessageTime > lastSeenTime) {
                           displayChatMessages(messages);
                           $('#chat-content').data('last-seen-time', lastMessageTime);
                        }
                     },
                     error: function(xhr, status, error) {
                        console.error('Error polling for new messages:', error);
                     }
                  });
               }, 3000);
            }

            window.closeChat = function() {
               clearInterval(messagePollingInterval);
            }

            let isSendingMessage = false;

            window.sendMessage = function() {
               if (isSendingMessage) return;

               isSendingMessage = true;

               const messageText = $('#chat-input').val();
               const friendId = $('#chat-friend-name').data('friend-id');

               if (messageText && friendId) {
                  $.ajax({
                     url: '<?= base_url("MessagesController/sendMessage") ?>',
                     type: 'POST',
                     data: {
                        message: messageText,
                        recipient_user_id: friendId
                     },
                     success: function(response) {
                        $('#chat-input').val('');
                        loadChatMessages(friendId);
                     },
                     error: function(xhr, status, error) {
                        console.error('Error sending message:', error);
                     },
                     complete: function() {
                        isSendingMessage = false;
                     }
                  });
               } else {
                  isSendingMessage = false;
               }
            };


            $('#chat-input').on('keydown', function(e) {
               if (e.which === 13 && !e.shiftKey) {
                  e.preventDefault();
                  window.sendMessage();
               }
            });


            function getRelativeTime(timestamp) {
               const now = new Date();
               const messageTime = new Date(timestamp);
               const diffInSeconds = Math.floor((now - messageTime) / 1000);

               // If message was sent within the last minute
               if (diffInSeconds < 60) {
                  return `${diffInSeconds} sec ago`;
               }

               const diffInMinutes = Math.floor(diffInSeconds / 60);
               if (diffInMinutes < 60) {
                  return `${diffInMinutes} min ago`;
               }

               const diffInHours = Math.floor(diffInMinutes / 60);
               if (diffInHours < 24) {
                  return `${diffInHours} hours ago`;
               }

               const diffInDays = Math.floor(diffInHours / 24);
               if (diffInDays === 1) {
                  return "Yesterday";
               }

               const options = {
                  month: 'long',
                  day: 'numeric'
               };
               return messageTime.toLocaleDateString('en-US', options);
            }


            function getUnreadCount() {
               $.ajax({
                  url: '<?= base_url("MessagesController/getUnreadCount") ?>',
                  type: 'GET',
                  success: function(data) {
                     const unreadCount = parseInt(data);
                     $('#unread-count').text(unreadCount);

                     if (unreadCount > 0) {
                        $('#unread-count').show();
                     } else {
                        $('#unread-count').hide();
                     }
                  },
                  error: function(xhr, status, error) {
                     console.error('Error fetching unread count:', error);
                  }
               });
            }


            function updateStatus() {
               $.ajax({
                  url: '<?php echo site_url("UserStatus/update_status"); ?>',
                  type: 'GET',
                  success: function(response) {

                  }
               });
            }


            function checkUserStatus(friendId) {
               if (friendId) {
                  $.ajax({
                     url: '<?= site_url("UserStatus/check_online/") ?>' + friendId,
                     type: 'GET',
                     success: function(response) {
                        if (response === "User is online") {
                           $('#status-span').text('Online').css('color', 'green');
                        } else if (response === "User is offline") {
                           $('#status-span').text('Offline').css('color', 'red');
                        } else {
                           $('#status-span').text('User not found').css('color', 'gray');
                        }
                     },
                     error: function() {
                        $('#status-span').text('Error checking status').css('color', 'gray');
                     }
                  });
               } else {
                  $('#status-span').text('User not found').css('color', 'gray');
               }
            }


            setInterval(updateStatus, 30000); // 30 seconds

            function startStatusInterval() {
               if (friendId) {
                  setInterval(function() {
                     if (friendId) {
                        checkUserStatus(friendId); // Pass the current friendId
                     }
                  }, 5000); // 5 seconds
               }
            }


         });
      </script>




      <script>
         $(document).ready(function() {

            $('#notification-toggle').on('click', function() {
               $('.notification').toggle(); // Toggles the visibility of the notification container
            });

            // Close button functionality to hide notifications
            $('.close-notification').on('click', function() {
               $('.notification').hide(); // Hides the notification container when the close button is clicked
            });

            // Handle click event on notification list items
            $('.notification-list').on('click', 'a', function(e) {
               e.preventDefault();
               var module_id = $(this).attr('data-module-id');

               // AJAX request to mark the module as notified
               $.ajax({
                  url: '<?php echo base_url("home/click_module/"); ?>' + module_id,
                  method: 'GET',
                  success: function(response) {
                     updateNotificationList();
                     updateNotificationCount(); // Update count when a notification is clicked
                     window.location.href = '<?php echo base_url("courses"); ?>';
                  }
               });
            });

            $.ajax({
               url: '<?php echo base_url("home/get_notification_count"); ?>',
               method: 'GET',
               success: function(data) {
                  var count = JSON.parse(data).count;
                  var $notificationCount = $('#notification-count');

                  // If there's a new notification count, display it
                  if (count > 0) {
                     if ($notificationCount.length === 0) {
                        $('.message-notify .bx-bell').after('<span id="notification-count">' + count + '</span>');
                     } else {
                        $notificationCount.text(count);
                     }
                  } else {
                     // If no new notifications, remove the count
                     $notificationCount.remove();
                  }
               }
            });

            function updateNotificationList() {
               $.ajax({
                  url: '<?php echo base_url("home/fetch_unnotified_modules"); ?>',
                  method: 'GET',
                  success: function(data) {
                     var notifications = JSON.parse(data);
                     var unnotifiedModules = notifications.unnotified_modules;
                     var statusModules = notifications.status_modules;

                     // Combine both arrays
                     var allNotifications = [...unnotifiedModules, ...statusModules];

                     // Sort by created_at in descending order
                     allNotifications.sort(function(a, b) {
                        return new Date(b.created_at) - new Date(a.created_at);
                     });

                     var notificationHtml = '';

                     if (allNotifications.length > 0) {
                        notificationHtml += '<ul>';

                        allNotifications.forEach(function(module) {
                           var createdAt = new Date(module.created_at);
                           var formattedDate = (createdAt.getMonth() + 1).toString().padStart(2, '0') + '.' +
                              createdAt.getDate().toString().padStart(2, '0') + '.' +
                              createdAt.getFullYear();

                           var message = module.module_status ?
                              (module.module_status === 'retake' ?
                                 `: You need to retake the module <strong>${module.course_name}</strong>. Keep improving!` :
                                 `: Congratulations! You have passed the module <strong>${module.course_name}</strong>.`) :
                              `: <span class="module-name-notif">${module.course_name}</span> has been added.`;

                           var liClass = module.module_status ?
                              (module.module_status === 'retake' ? 'notification-retake' : 'notification-pass') :
                              'notification-new';

                           notificationHtml += `
                                 <li class="${liClass}">
                                       <a href="#" data-module-id="${module.module_id}" class="notification-link">
                                          <strong>${module.module_status ? 'Module Update' : 'New Module'}</strong>
                                          <span class="notification-text">${message}</span>
                                          <span class="notification-date">Date: ${formattedDate}</span>
                                       </a>
                                 </li>
                              `;
                        });

                        notificationHtml += '</ul>';
                     } else {
                        notificationHtml = '<p>No new notifications.</p>';
                     }

                     $('.notification-list').html(notificationHtml); // Update the notification list in the UI
                  }
               });
            }

            // Function to update the notification count
            function updateNotificationCount() {
               $.ajax({
                  url: '<?php echo base_url("home/fetch_notification_count"); ?>',
                  method: 'GET',
                  success: function(data) {
                     var count = JSON.parse(data).count;
                     var $notificationCount = $('#notification-count');

                     if (count > 0) {
                        if ($notificationCount.length === 0) {
                           $('.message-notify .bx-bell').after('<span id="notification-count">' + count + '</span>');
                        } else {
                           $notificationCount.text(count);
                        }
                     } else {
                        $notificationCount.remove();
                     }
                  }
               });
            }

            updateNotificationList(); // Initial call to update the list
         });
      </script>

      <script>
         $(document).on('click', '.notification-link', function(e) {
            e.preventDefault();
            var moduleId = $(this).data('module-id'); // Get the module ID from the data attribute

            // Send an AJAX request to mark the notification as read
            $.ajax({
               url: '<?php echo base_url("home/mark_notification_as_read"); ?>',
               method: 'POST',
               data: {
                  module_id: moduleId
               },
               success: function(response) {
                  var result = JSON.parse(response);
                  if (result.success) {
                     // Optionally, remove the notification from the list or mark it visually as read
                     SmartAlerts.success('Notification has been marked as read.', 'Notification Updated');
                  } else {
                     SmartAlerts.error('Unable to mark notification as read. Please try again.', 'Update Failed');
                  }
               },
               error: function() {
                  SmartAlerts.error('A network error occurred while updating the notification.', 'Connection Error');
               }
            });
         });
      </script>