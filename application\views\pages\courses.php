
   
<!-- Custom Modal HTML -->
<div id="customModal" class="custom-modal">
   <div class="custom-modal-content">
      <div class="module-thumbnail">
            <!-- Make sure to give the img an id for JavaScript targeting -->
            <img id="modalCourseImage" src="" alt="Course Image">
      </div>
      <div class="module-content-confirm">
            <div class="module-content-title">
               <h2 id="modalCourseName"></h2>
               <p id="modalCourseDesignation"></p>
               <p id="modalCourseDescription"></p>
            </div>

            <div class="module-content-button">
               <button id="confirmStartButton" class="confirm-btn">Start Module now</button>
               <button class="cancel-btn">Cancel</button>
            </div>
      </div>
   </div>
   <div class="closeBtn-course-container">
      <button id="closeBtn-course">Cancel and Exit Module</button>
   </div>
</div>


<div class="nav-container">
      <div class="small-container">
            <div class="course-nav explore-course-nav">
               <div class="progress">
                  <ul>
                        <li><h2>Explore Courses</h2></li>
                  </ul>
               </div>

               <div class="explore-course-filter">
                  <!-- Search bar for Course Name -->
                  <div class="filter-input">
                        <input 
                           type="text" 
                           name="course_name" 
                           id="filterByCourseName" 
                           placeholder="Search by Course Name" 
                           aria-label="Search by Course Name"
                        />
                  </div>

                  <!-- Date input for Created Date -->
                  <div class="filter-input">
                        <input 
                           type="date" 
                           name="created_at" 
                           id="filterByDateCreated" 
                           aria-label="Filter by Date Created"
                        />
                  </div>
               </div>

            </div>


            <?php 
               $userID = $this->session->userdata('id');
               $logged_in = $this->session->userdata('email');
            ?>

            <div class="course-card-container">
               <?php if (!empty($courses)): ?>
                  <?php foreach ($courses as $course): ?>

                  <div 
                        class="course-card"
                        data-course-name="<?php echo htmlspecialchars(strtolower($course->course_name), ENT_QUOTES, 'UTF-8'); ?>"
                        data-created-at="<?php echo date('Y-m-d', strtotime($course->created_at)); ?>"
                  >
                        
                        <div class="course-content-title">
                           <h2><?php echo htmlspecialchars($course->course_name, ENT_QUOTES, 'UTF-8'); ?></h2>
                           <p><?php echo htmlspecialchars($course->designation, ENT_QUOTES, 'UTF-8'); ?></p>
                           <p><?php echo htmlspecialchars($course->description, ENT_QUOTES, 'UTF-8'); ?></p>

                           <?php if (isset($course->attempt_count) && $course->attempt_count > 0): ?>
                              <span class="retake-label">Retake Module</span>
                           <?php endif; ?>

                        </div>
                        
                        <?php if ($logged_in): ?>
                        <!-- Start Course Button -->
                        <div class="course-content-button">
                           <button 
                              class="start-course-button" 
                              data-course-id="<?php echo $course->module_id; ?>" 
                              data-course-name="<?php echo $course->course_name; ?>" 
                              data-course-designation="<?php echo $course->designation; ?>" 
                              data-course-description="<?php echo $course->description; ?>"
                              data-course-image="<?php echo htmlspecialchars($course->image_url, ENT_QUOTES, 'UTF-8'); ?>"
                              aria-label="Start Module now">
                              Start Module now &#8594;
                           </button>
                        </div>  

                     
                        <?php else: ?>
                           <!-- Handle case when user is not logged in -->
                           <div class="course-content-button">
                              <button id="start-course-btn" class="start-course-btn">Sign in to start this module</button>
                           </div>
                        <?php endif; ?>
                  </div>
               <?php endforeach; ?>
               <?php else: ?>
                  <!-- Display message when no courses are available -->
                  <p class="no-modules-message">No module available</p>
               <?php endif; ?>
            </div>
   </div>
</div>


<div class="course-container">
   <div class="small-container">

      <div class="course-left" data-aos="fade-in">
            <img src="<?php echo !empty($module->image_url) ? $module->image_url : base_url('assets/images/library.webp'); ?>" alt="Mountain Peak" loading="lazy" class="scroll-image course-left-img" id="scrollImage">
      </div>

      <?php if (!empty($modules)): ?>
            <?php foreach ($modules as $module): ?>
               <div class="course-right" data-aos="fade-left" data-aos-delay="100">
                  <div class="course-right-item">

                     <div class="general-course-container">
                           <small class="prerequisite">General Module</small>
                           <div class="next-button-container">
                              <ion-icon name="arrow-redo-outline"></ion-icon>
                              <a href="<?php echo site_url('courses/index/' . $next_offset); ?>" class="next-button">
                                 Next Module
                              </a>
                           </div>
                        </div>
                           
                        <h2><?php echo $module->course_name; ?></h2>
                        <p class="course-desc"><?php echo $module->description; ?></p>

                        <div class="div-item">
                           <p><?php echo $module->designation; ?></p>
                        </div>

                        <div class="div-item">
                           <p>For All Employee</p>
                        </div>

                        <?php 
                           $userID = $this->session->userdata('id');
                           $logged_in = $this->session->userdata('email');
                        ?>
                        <?php if ($logged_in): ?>
                           <button 
                              class="start-course-button pre-req-btn" 
                              data-course-id="<?php echo $module->module_id; ?>" 
                              data-course-name="<?php echo $module->course_name; ?>" 
                              data-course-designation="<?php echo $module->designation; ?>" 
                              data-course-description="<?php echo $module->description; ?>"
                              data-course-image="<?php echo htmlspecialchars($module->image_url, ENT_QUOTES, 'UTF-8'); ?>"
                              aria-label="Start Module now">
                              Start Module now
                           </button>
                        <?php else: ?>
                           <button>Sign in to start this module</button>
                        <?php endif; ?>
                  </div>

                  


               </div>
            <?php endforeach; ?>

      <?php else: ?>
            <p class="no-module-available">No modules available for All Business Unit</p>
      <?php endif; ?>

   </div>
</div>



<div class="mindset-container">
   <div class="small-container">
      <div class="mindset-item" data-aos="fade-left">
            <div class="mindset-content">
               <div class="content-m">
                  <h2>Mindset for Continuous Learning</h2>
                  <p>Learning is a journey, not a destination. Completing a course is just the beginning. To truly master a subject, it’s important to continuously push yourself and embrace new challenges.</p>
               </div>
               <div class="mindset-list">
                  <p>Enhances Problem-Solving Abilities</p>
                  <p>Opens New Opportunities</p>
                  <p>Fosters Creativity and Innovation</p>
                  <a href="<?php echo base_url('resume'); ?>">Resume Learning</a>
               </div>
            </div>
      </div>
      <div class="mindset-item" data-aos="fade-right">
            <img src="<?php echo base_url('assets/images/learning-mindset.webp'); ?>" alt="Mountain Peak" loading="lazy" class="scroll-image course-left-img" id="scrollImage">
      </div>
   </div>
</div>



<script>
   AOS.init();
</script>

<script>
   window.addEventListener('scroll', function() {
      var scrollY = window.scrollY;
      var image = document.getElementById('scrollImage');

      // Adjust the width based on scroll position
      // You can customize the formula to suit your needs
      var newWidth = 100 + scrollY / 25;
      image.style.width = newWidth + '%';
   });
</script>


<script>
   document.addEventListener('DOMContentLoaded', (event) => {
      const courseCardContainer = document.querySelector('.nav-container');
      if (courseCardContainer) {
            const textElements = courseCardContainer.querySelectorAll('small, p, h4, .course-card-container');
            textElements.forEach(element => {
               element.addEventListener('mousedown', (e) => {
                  e.preventDefault();
               });
            });
      }
   });
</script>


<script>
   function disableTextSelection() {
      const elements = document.querySelectorAll('.course-card-container');
      elements.forEach(element => {
            element.style.userSelect = 'none'; // For modern browsers
            element.style.MozUserSelect = 'none'; // For Firefox
            element.style.msUserSelect = 'none'; // For Internet Explorer and Edge
            element.style.webkitUserSelect = 'none'; // For Safari
      });
   }

   // Call the function to apply the style
   disableTextSelection();
</script>

<script>
   document.addEventListener('DOMContentLoaded', function() {
      const container = document.querySelector('.course-card-container');
      let isDown = false;
      let startX;
      let scrollLeft;

      container.addEventListener('mousedown', (e) => {
            isDown = true;
            container.classList.add('active');
            startX = e.pageX - container.offsetLeft;
            scrollLeft = container.scrollLeft;
      });

      container.addEventListener('mouseleave', () => {
            isDown = false;
            container.classList.remove('active');
      });

      container.addEventListener('mouseup', () => {
            isDown = false;
            container.classList.remove('active');
      });

      container.addEventListener('mousemove', (e) => {
            if(!isDown) return; // stop the fn from running
            e.preventDefault();
            const x = e.pageX - container.offsetLeft;
            const walk = (x - startX) * 2; //scroll-fast
            container.scrollLeft = scrollLeft - walk;
      });
   });
</script>


<script>
   document.addEventListener('DOMContentLoaded', () => {
      const courseSearch = document.getElementById('filterByCourseName');
      const dateFilter = document.getElementById('filterByDateCreated');
      const courseItems = document.querySelectorAll('.course-card');

      // Search by course name
      courseSearch.addEventListener('input', () => {
            const query = courseSearch.value.toLowerCase();
            courseItems.forEach(item => {
               const courseName = item.getAttribute('data-course-name');
               item.style.display = courseName.includes(query) ? '' : 'none';
            });
      });

      // Filter by date created
      dateFilter.addEventListener('change', () => {
            const selectedDate = dateFilter.value;
            courseItems.forEach(item => {
               const courseDate = item.getAttribute('data-created-at');

               // If no date is selected, show all items
               if (!selectedDate || courseDate === selectedDate) {
                  item.style.display = '';
               } else {
                  item.style.display = 'none';
               }
            });
      });
   });
</script>

<script>
   $(document).ready(function() {
      var enrolledCourses = {}; // Object to keep track of enrolled courses
      var selectedCourseId = null; // To store the selected course ID for confirmation
      var defaultImage = "assets/images/library.webp"; // Define your default image URL here

      // Show modal when 'Start Course' button is clicked
      $(document).on('click', '.start-course-button', function(event) {
            event.preventDefault(); // Prevent default button action

            // Retrieve course details from data attributes
            selectedCourseId = $(this).data('course-id');
            var courseName = $(this).data('course-name');
            var courseDescription = $(this).data('course-description');
            var courseDesignation = $(this).data('course-designation');
            var courseImage = $(this).data('course-image'); // Get the image URL from data attribute

            // If courseImage is empty or undefined, use the default image
            if (!courseImage || courseImage.trim() === '') {
               courseImage = defaultImage;
            }

            // Set course name, description, designation, and image in the modal
            $('#modalCourseName').text(courseName);
            $('#modalCourseDescription').text(courseDescription);
            $('#modalCourseDesignation').text(courseDesignation);
            $('#modalCourseImage').attr('src', courseImage); // Set the course image or default image

            // Show the custom modal
            $('#customModal').show();
      });

      // Close the modal when the user clicks the close button or cancel
      $('#closeBtn-course, .cancel-btn').on('click', function() {
            $('#customModal').hide(); // Hide the modal
      });

      // Event handler for confirmation button in the modal
      $('#confirmStartButton').on('click', function() {
            if (!selectedCourseId) {
               console.log('Missing courseId'); // Debugging in case of error
               return;
            }

            var userID = <?php echo json_encode($userID); ?>; // Get user ID from session

            // Check if this course has already been enrolled
            if (enrolledCourses[selectedCourseId]) {
               return; // Stop if already enrolled
            }

            // Mark course as enrolled
            enrolledCourses[selectedCourseId] = true;

            // Make AJAX call to enroll the user
            $.ajax({
               url: '<?php echo site_url('courses/enroll'); ?>',
               type: 'POST',
               data: {
                  user_id: userID,
                  module_id: selectedCourseId,
                  course_status: 1
               },
               dataType: 'json',
               success: function(response) {
                  if (response.status === 'success') {
                        SmartAlerts.success('Successfully enrolled in the course!', 'Enrollment Complete');
                        setTimeout(() => {
                           window.location.href = "<?php echo site_url('courses/view/'); ?>" + selectedCourseId; // Redirect to course page
                        }, 1500);
                  } else if (response.status === 'error') {
                        SmartAlerts.error(response.message, 'Enrollment Failed'); // Display error message
                  }
               },
               error: function(xhr, status, error) {
                  console.error('An error occurred: ', error);
                  console.error('Response: ', xhr.responseText);
               }
            });

            // Hide the modal after confirmation
            $('#customModal').hide();
      });
   });
</script>