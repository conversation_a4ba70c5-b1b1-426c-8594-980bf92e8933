/**
 * Simple Smart Alerts System - Minimal Version
 */

(function() {
    'use strict';
    
    // Simple Smart Alerts Class
    function SmartAlerts() {
        this.container = null;
        this.alertCounter = 0;
        this.init();
    }

    SmartAlerts.prototype.init = function() {
        // Create container
        this.container = document.createElement('div');
        this.container.className = 'smart-alert-container';
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
        `;
        document.body.appendChild(this.container);
        console.log('SmartAlerts container created');
    };

    SmartAlerts.prototype.show = function(message, type, title, duration) {
        type = type || 'info';
        title = title || this.getDefaultTitle(type);
        duration = duration || 5000;
        
        var alertId = ++this.alertCounter;
        var alertElement = this.createAlert(alertId, message, type, title);
        
        this.container.appendChild(alertElement);
        
        // Show animation
        setTimeout(function() {
            alertElement.style.transform = 'translateX(0)';
        }, 10);
        
        // Auto hide
        if (duration > 0) {
            setTimeout(function() {
                alertElement.style.transform = 'translateX(420px)';
                alertElement.style.opacity = '0';
                setTimeout(function() {
                    if (alertElement.parentNode) {
                        alertElement.parentNode.removeChild(alertElement);
                    }
                }, 300);
            }, duration);
        }
        
        return alertId;
    };

    SmartAlerts.prototype.createAlert = function(id, message, type, title) {
        var alert = document.createElement('div');
        alert.className = 'smart-alert smart-alert-' + type;
        alert.style.cssText = `
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            margin-bottom: 12px;
            padding: 16px 20px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
            transform: translateX(420px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-left: 4px solid ${this.getColor(type)};
            position: relative;
            overflow: hidden;
        `;
        
        var icon = this.getIcon(type);
        
        alert.innerHTML = `
            <div style="
                flex-shrink: 0;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                color: #fff;
                font-weight: bold;
                background: ${this.getColor(type)};
            ">${icon}</div>
            <div style="flex: 1; min-width: 0;">
                <div style="
                    font-weight: 600;
                    font-size: 14px;
                    margin: 0 0 4px 0;
                    color: #1f2937;
                    line-height: 1.4;
                ">${title}</div>
                <div style="
                    font-size: 13px;
                    color: #6b7280;
                    margin: 0;
                    line-height: 1.4;
                    word-wrap: break-word;
                ">${message}</div>
            </div>
            <button onclick="this.parentNode.style.display='none'" style="
                position: absolute;
                top: 8px;
                right: 8px;
                background: none;
                border: none;
                color: #9ca3af;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                font-size: 16px;
                line-height: 1;
                width: 24px;
                height: 24px;
            ">&times;</button>
        `;
        
        return alert;
    };

    SmartAlerts.prototype.getColor = function(type) {
        var colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        return colors[type] || colors.info;
    };

    SmartAlerts.prototype.getIcon = function(type) {
        var icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'i'
        };
        return icons[type] || icons.info;
    };

    SmartAlerts.prototype.getDefaultTitle = function(type) {
        var titles = {
            success: 'Success',
            error: 'Error',
            warning: 'Warning',
            info: 'Information'
        };
        return titles[type] || titles.info;
    };

    // Convenience methods
    SmartAlerts.prototype.success = function(message, title) {
        return this.show(message, 'success', title, 4000);
    };

    SmartAlerts.prototype.error = function(message, title) {
        return this.show(message, 'error', title, 6000);
    };

    SmartAlerts.prototype.warning = function(message, title) {
        return this.show(message, 'warning', title, 5000);
    };

    SmartAlerts.prototype.info = function(message, title) {
        return this.show(message, 'info', title, 5000);
    };

    // Initialize when DOM is ready
    function initSmartAlerts() {
        // Create global instance
        window.SmartAlerts = new SmartAlerts();
        
        // Override alert function
        window.originalAlert = window.alert;
        window.alert = function(message) {
            var type = 'info';
            var title = '';
            
            var msg = message.toLowerCase();
            if (msg.includes('error') || msg.includes('failed') || msg.includes('network')) {
                type = 'error';
                title = 'Error';
            } else if (msg.includes('success') || msg.includes('completed') || msg.includes('marked as')) {
                type = 'success';
                title = 'Success';
            } else if (msg.includes('please') || msg.includes('select') || msg.includes('provide')) {
                type = 'warning';
                title = 'Action Required';
            }
            
            window.SmartAlerts.show(message, type, title);
        };
        
        console.log('SmartAlerts initialized successfully!');
    }

    // Initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSmartAlerts);
    } else {
        initSmartAlerts();
    }

})();
