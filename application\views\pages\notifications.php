<script>
   var csrfName = '<?php echo $this->security->get_csrf_token_name(); ?>'; // e.g. csrf_test_name
   var csrfHash = '<?php echo $this->security->get_csrf_hash(); ?>'; // The actual token hash
</script>


<div class="main-container">
   <div class="small-container">
      <div class="notifications-page">
         <main id="notifications-container">
            <!-- Notifications dynamically loaded here -->
         </main>
      </div>
   </div>
</div>


<script>
$(document).ready(function() {
  // Load notifications on page load
  loadNotifications();

  function loadNotifications() {
    $.ajax({
      url: '<?php echo base_url("home/fetch_unnotified_modules"); ?>',
      method: 'GET',
      success: function(data) {
        var notifications = JSON.parse(data);
        var unnotifiedModules = notifications.unnotified_modules;
        var statusModules = notifications.status_modules;
        var allNotifications = [...unnotifiedModules, ...statusModules];
        allNotifications.sort(function(a, b) {
          return new Date(b.created_at) - new Date(a.created_at);
        });

        var html = '';
        if (allNotifications.length > 0) {
          html += '<ul class="notification-list">';
          allNotifications.forEach(function(module) {
            var createdAt = new Date(module.created_at);
            var formattedDate = (createdAt.getMonth() + 1).toString().padStart(2, '0') + '.' +
                                createdAt.getDate().toString().padStart(2, '0') + '.' +
                                createdAt.getFullYear();
            var message = module.module_status
              ? (module.module_status === 'retake'
                  ? ': You need to retake the module <strong>' + module.course_name + '</strong>. Keep improving!'
                  : ': Congratulations! You have passed the module <strong>' + module.course_name + '</strong>.')
              : ': <span class="module-name-notif">' + module.course_name + '</span> has been added.';
            var liClass = module.module_status
              ? (module.module_status === 'retake' ? 'notification-retake' : 'notification-pass')
              : 'notification-new';
            html += `
              <li class="${liClass}">
                <a href="#" data-module-id="${module.module_id}" class="notification-link">
                  <strong>${module.module_status ? 'Module Update' : 'New Module'}</strong>
                  <span class="notification-text">${message}</span>
                  <span class="notification-date">${formattedDate}</span>
                </a>
              </li>
            `;
          });
          html += '</ul>';
        } else {
          html = '<p>No new notifications.</p>';
        }
        $('#notifications-container').html(html);
      }
    });
  }

  $(document).on('click', '.notification-link', function(e) {
    e.preventDefault();
    var moduleId = $(this).data('module-id');

    $.ajax({
      url: '<?php echo base_url("home/mark_notification_as_read"); ?>',
      method: 'POST',
      data: {
         module_id: moduleId,
         [csrfName]: csrfHash // Add the CSRF token here
      },
      success: function(response) {
         var result = JSON.parse(response);
         if (result.success) {
            window.location.href = '<?php echo base_url("courses"); ?>';
         } else {
            SmartAlerts.error('Unable to mark notification as read. Please try again.', 'Update Failed');
         }
      },
      error: function() {
         SmartAlerts.error('A network error occurred while updating the notification.', 'Connection Error');
      }
   });



  });
});
</script>

