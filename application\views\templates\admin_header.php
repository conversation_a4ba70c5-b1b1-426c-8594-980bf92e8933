<!DOCTYPE html>
<html lang="en">

<head>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <meta name="description" content="New Employee Onboarding Admin Panel">

   <!-- Boxicons -->
   <link href='https://unpkg.com/boxicons@2.0.9/css/boxicons.min.css' rel='stylesheet'>
   <link rel="preconnect" href="https://fonts.googleapis.com">
   <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
   <link href="https://fonts.googleapis.com/css2?family=Fjalla+One&display=swap" rel="stylesheet">
   <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Yantramanav:wght@100;300;400;500;700;900&display=swap" rel="stylesheet">
   <link rel="icon" href="<?= base_url('favicon.ico'); ?>" type="image/x-icon">

   <!-- My CSS -->
   <link rel="stylesheet" href="<?= base_url('css/evaluation_details.css'); ?>">
   <link rel="stylesheet" href="<?= base_url('css/evaluations.css'); ?>">
   <link rel="stylesheet" href="<?= base_url('css/enrollments.css'); ?>">
   <link rel="stylesheet" href="<?= base_url('css/draft_module.css'); ?>">
   <link rel="stylesheet" href="<?= base_url('css/enroll_admin.css'); ?>">
   <link rel="stylesheet" href="<?= base_url('css/report.css'); ?>">
   <link rel="stylesheet" href="<?= base_url('css/users.css'); ?>">
   <link rel="stylesheet" href="<?= base_url('css/message.css'); ?>">
   <link rel="stylesheet" href="<?= base_url('css/edit.css'); ?>">
   <link rel="stylesheet" href="<?= base_url('css/manage_course.css'); ?>">
   <link rel="stylesheet" href="<?= base_url('css/admin_course.css'); ?>">
   <link rel="stylesheet" href="<?= base_url('css/admin_header.css'); ?>">
   <link rel="stylesheet" href="<?= base_url('css/admin.css'); ?>">
   <link rel="stylesheet" href="<?= base_url('css/create_course.css'); ?>">
   <link rel="stylesheet" href="<?= base_url('css/onboarding.css'); ?>">

   <link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
   <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
   <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>

   <link href="https://api.fontshare.com/v2/css?f[]=general-sans@200,201,300,301,400,401,500,501,600,601,700,701,1,2&display=swap" rel="stylesheet">

   <!-- JQuery -->
   <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
   <script src="https://www.gstatic.com/charts/loader.js"></script>

   <!-- Ionicons -->
   <script type="module" src="https://cdn.jsdelivr.net/npm/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
   <script nomodule src="https://cdn.jsdelivr.net/npm/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>

   <!-- Smart Alerts System -->
   <script src="<?= base_url('assets/js/smart-alerts-simple.js') ?>"></script>


   <script>
      // Prevent HTML input in text inputs and textareas
      document.addEventListener('DOMContentLoaded', function() {
         const textInputs = document.querySelectorAll('input[type="text"], textarea');
         textInputs.forEach(function(input) {
            input.addEventListener('input', function() {
               this.value = this.value.replace(/<\/?[^>]*>/g, '');
            });
         });
      });
   </script>

   <script>
      function escapeHtml(text) {
         return text
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
      }
   </script>

   <title>BLS Management System</title>
</head>


<body class="dark">
   <!-- SIDEBAR -->
   <section id="sidebar">

      <div class="title-container">
         <i class="bx"><img class="bls-icon" id="bls-icon" src="<?php echo base_url('assets/icons/bls-logo.webp'); ?>" alt="Description of the image" loading="lazy"></i>
      </div>


      <ul class="side-menu top">

         <?php
         $currentController = $this->uri->segment(1);
         $currentMethod = $this->uri->segment(2);
         $isDashboard = ($currentController == '' || ($currentController == 'admin' && ($currentMethod == '' || $currentMethod == 'index')));
         ?>

         <li class="<?= $isDashboard ? 'active' : ''; ?>">
            <a href="<?php echo base_url(); ?>">
               <i class='bx'><ion-icon name="grid-outline"></ion-icon></i>
               <span class="text">Dashboard</span>
            </a>
         </li>

         <li>
            <a href="<?php echo base_url('admin_course'); ?>">
               <i class='bx'><ion-icon name="book-outline"></ion-icon></i>
               <span class="text">Manage Modules</span>
            </a>
         </li>

         <li>
            <a href="<?php echo base_url('enroll_admin'); ?>">
               <i class='bx'><ion-icon name="newspaper-outline"></ion-icon></i>
               <span class="text">Manage Admin Role</span>
            </a>
         </li>

         <li>
            <a href="<?php echo base_url('users'); ?>">
               <i class='bx'><ion-icon name="people-outline"></ion-icon></i>
               <span class="text">Employees</span>
            </a>
         </li>

         <li>
            <a href="<?php echo base_url('message'); ?>">
               <i class='bx'><ion-icon name="chatbubbles-outline"></ion-icon></i>
               <span class="text">Message</span>
            </a>
         </li>

         <li>
            <a href="<?php echo base_url('evaluations'); ?>">
               <i class='bx'><ion-icon name="albums-outline"></ion-icon></i>
               <span class="text">Evaluation Queue</span>
            </a>
         </li>

         <li>
            <a href="<?php echo base_url('blocked_retakers'); ?>">
               <i class='bx'><ion-icon name="ban-outline"></ion-icon></i>
               <span class="text">Restricted Retakers</span>
            </a>
         </li>

         <li class="report">
            <a href="<?php echo base_url('report'); ?>">
               <i class='bx'><ion-icon name="document-outline"></ion-icon></i>
               <span class="text">Reports</span>
            </a>
            <div class="sub-report">
               <a href="<?php echo base_url('progress_report'); ?>">Progress Report</a>
               <a href="<?php echo base_url('retake_report'); ?>">Retake Report</a>
               <a href="<?php echo base_url('completion_report'); ?>">Completion Report</a>
            </div>
         </li>

         <script>
            document.addEventListener('DOMContentLoaded', function() {
               // Get the current page URL
               const currentUrl = window.location.href;

               const reportsMenuItem = document.querySelector('.report');
               const mainReportLink = reportsMenuItem.querySelector('a[href*="report"]');

               const subReportLinks = document.querySelectorAll('.sub-report a');

               // Check each sub-report link
               subReportLinks.forEach(link => {
                  // If the current URL includes the link's href
                  if (currentUrl.includes(link.getAttribute('href'))) {
                     // Add active class to the main reports menu item
                     reportsMenuItem.classList.add('active');

                     // Change the color of the active sub-menu item
                     link.style.color = 'var(--btn-blue) !important';
                  }
               });

               // Check if the main report page is active
               if (currentUrl.includes(mainReportLink.getAttribute('href'))) {
                  // Add active class to the main reports menu item
                  reportsMenuItem.classList.add('active');

                  // Change the color of the first sub-menu item to red
                  const firstSubMenuLink = subReportLinks[0];
                  firstSubMenuLink.style.color = 'var(--btn-blue) !important';
               }
            });
         </script>

         <li class="create-course">
            <a href="<?= base_url('create_course'); ?>" class="nav-link create-button">
               <i class='bx bxs-plus-circle'></i>
               <span class="text">Create Module</span>
            </a>
         </li>
      </ul>

      <div class="sidebar-bottom">
         <ul class="side-menu new-side-menu">
            <li>
               <a href="#" class="user-profile-container">
                  <i class='bx'><ion-icon name="person-outline"></ion-icon></i>
                  <span class="text">

                     <?php
                     // Retrieve session data
                     $first_name = $this->session->userdata('first_name');
                     $last_name = $this->session->userdata('last_name');
                     ?>
                     <p class="user-name">BLS Admin &mdash; <?php echo $first_name . ' ' . $last_name; ?></p>
                  </span>
               </a>
            </li>
         </ul>

         <ul class="side-menu new-side-menu">
            <li class="logout-container">
               <a href="<?php echo site_url('home/logout'); ?>">
                  <i class='bx'><ion-icon name="log-out-outline"></ion-icon></i>
                  <span class="text">Logout</span>
               </a>
            </li>
         </ul>
      </div>

   </section>
   <!-- SIDEBAR -->




   <!-- CONTENT -->
   <section id="content">
      <!-- NAVBAR -->
      <nav>
         <div class="nav-left">
            <i class='bx bx-menu header-item' id="menu-icon"></i>

            <form action="#" class="header-item">
               <div class="form-input">
                  <input type="search" placeholder="Search navigation" aria-label="Search" class="search" id="searchBar">
                  <button type="submit" class="search-btn">
                     <i class='bx bx-search'></i>
                     <span class="visually-hidden">Search</span>
                  </button>
               </div>
               <div class="search-results" id="searchResults">
               </div> <!-- Container for dynamic results -->
            </form>

         </div>

         <div class="nav-right">

            <a href="#" id="message-icon" class="header-item badge-indicator" aria-label="Messages">
               <ion-icon name="chatbubbles-outline"></ion-icon>
               <small>Messages</small>
               <span id="unread-count">8</span>
            </a>

            <!-- Notification Icon -->
            <a href="#" id="notification" class="notification header-item badge-indicator" aria-label="Notifications">
               <ion-icon name="notifications-outline"></ion-icon>
               <small>Notifications</small>
               <span id="unread-notification"></span>
            </a>

            <!-- Notifications Panel (Initially Hidden) -->
            <div id="unread-notifications" class="notifications-panel" style="display:none;">
               <!-- Notifications will be dynamically inserted here -->
            </div>




            <script>
               document.addEventListener('DOMContentLoaded', () => {
                  const searchBar = document.getElementById('searchBar');
                  const searchResults = document.getElementById('searchResults');

                  // Base URL from PHP
                  const baseUrl = '<?php echo base_url(); ?>';

                  // Predefined keywords for each page
                  const pageKeywords = [{
                        page: 'Dashboard',
                        url: baseUrl + 'admin',
                        keywords: ['dashboard', 'new employee onboarding', 'employee management', 'onboarding completion rate', 'course completion rate']
                     },
                     {
                        page: 'Courses',
                        url: baseUrl + 'admin_course',
                        keywords: ['module', 'recently added courses', 'draft module', 'courses']
                     },
                     {
                        page: 'Enrollment',
                        url: baseUrl + 'onboarding',
                        keywords: ['information', 'add new admin', 'enrollment list', 'enroll employee to a course', 'search for module', 'search for employee', 'module information', 'selected users']
                     },
                     {
                        page: 'Employees',
                        url: baseUrl + 'users',
                        keywords: ['employees', 'onboarding employees', 'transfer status', 'all employees']
                     },
                     {
                        page: 'Message',
                        url: baseUrl + 'message',
                        keywords: ['message', 'recent messages', 'bulk message', 'selected recipients']
                     },
                     {
                        page: 'Report',
                        url: baseUrl + 'report',
                        keywords: ['report', 'analytics', 'statistics', 'logs']
                     },
                     {
                        page: 'Create Module',
                        url: baseUrl + 'create_course',
                        keywords: ['create', 'new course', 'create module', 'add']
                     },
                     {
                        page: 'Enrollments List',
                        url: baseUrl + 'enrollments',
                        keywords: ['enrollments', 'list', 'students', 'enroll', 'courses']
                     },
                     {
                        page: 'Admin Management',
                        url: baseUrl + 'enroll_admin',
                        keywords: ['manage', 'management', 'admin', 'enroll', 'privileges']
                     },
                  ];

                  // Event listener for user input
                  searchBar.addEventListener('input', (event) => {
                     const query = event.target.value.toLowerCase();

                     // Clear previous results
                     searchResults.innerHTML = '<h3>Navigagation Search Results</h3>';
                     searchResults.style.display = 'none';
                     searchResults.style.border = 'none';
                     searchResults.style.padding = '0';

                     if (query) {
                        // Filter keywords matching the query
                        const results = [];
                        pageKeywords.forEach(page => {
                           page.keywords.forEach(keyword => {
                              if (keyword.toLowerCase().includes(query)) {
                                 results.push({
                                    page: page.page,
                                    keyword,
                                    url: page.url
                                 });
                              }
                           });
                        });

                        // Generate result items
                        if (results.length > 0) {
                           results.forEach(result => {
                              const resultItem = document.createElement('div');
                              resultItem.classList.add('result-item');
                              resultItem.textContent = `${result.page}: ${result.keyword}`;
                              resultItem.addEventListener('click', () => {
                                 // Navigate to the page on click
                                 window.location.href = result.url;
                              });
                              searchResults.appendChild(resultItem);
                           });
                           searchResults.style.display = 'block'; // Show results
                           searchResults.style.border = '1px solid var(--border-to)';
                           searchResults.style.padding = '12px';
                        }
                     }
                  });

                  // Hide results when clicking outside the search bar
                  document.addEventListener('click', (event) => {
                     if (!event.target.closest('.header-item')) {
                        searchResults.style.display = 'none';
                     }
                  });
               });
            </script>


            <script>
               $(document).ready(function() {
                  // Fetch the count of unread courses (where is_read is NULL)
                  $.ajax({
                     url: '<?php echo site_url('ReportController/get_unread_course_count'); ?>', // Fetch the unread course count
                     method: 'GET',
                     success: function(data) {
                        if (data === '0' || data === '') {
                           $('#unread-notification').hide(); // Hide the span if it is empty
                        } else {
                           $('#unread-notification').text(data); // Update the unread notification count
                           $('#unread-notification').show(); // Show the span if it is not empty
                        }
                     },
                     error: function(xhr, status, error) {
                        console.error('Error retrieving unread course count: ' + error);
                     }
                  });

                  // Fetch unread notifications (where is_read is NULL)
                  $.ajax({
                     url: '<?php echo site_url('ReportController/get_unread_notifications'); ?>', // Fetch unread notifications
                     method: 'GET',
                     dataType: 'json',
                     success: function(notifications) {
                        let html = '<div class="notification-header" id="hide-notifications"><h4>Unread Notifications</h4><button>Hide Notifications</button></div>';

                        if (notifications.length > 0) {
                           html += '<ul>';
                           $.each(notifications, function(index, notification) {
                              let dateObj = new Date(notification.date);

                              // Format to "DD MON YYYY", e.g., 07 JUL 2025
                              let day = String(dateObj.getDate()).padStart(2, '0');
                              let month = dateObj.toLocaleString('en-US', {
                                 month: 'short'
                              }).toUpperCase();
                              let year = dateObj.getFullYear();
                              let formattedDate = `${day} ${month} ${year}`;

                              let notificationText = `${notification.first_name} ${notification.last_name} 
                              finished the course <strong>${notification.course_name}</strong>`;

                              html += '<li data-id="' + notification.id + '">';
                              html += `<span>${notificationText}</span><br><small class="formatted-date">${formattedDate}</small>`;
                              html += '</li>';
                           });
                           html += '</ul>';
                        } else {
                           html += '<p>No unread notifications.</p>';
                        }

                        $('#unread-notifications').html(html); // Insert the notifications into the panel
                     },
                     error: function(xhr, status, error) {
                        console.error('Error retrieving unread notifications: ' + error);
                        $('#unread-notifications').html('<p>Error retrieving notifications.</p>');
                     }
                  });



                  // Toggle the visibility of the notifications panel when the notification icon is clicked
                  $('#notification').click(function(event) {
                     event.preventDefault(); // Prevent the default action of the link
                     $('#unread-notifications').toggle(); // Toggle the display of the notifications panel
                  });

                  // Handle notification item click (mark as read and redirect)
                  $(document).on('click', '#unread-notifications li', function() {
                     var notificationId = $(this).data('id'); // Get the notification ID
                     console.log('Notification ID: ' + notificationId); // Debugging log

                     $.ajax({
                        url: '<?php echo site_url('ReportController/mark_notification_read'); ?>',
                        method: 'POST',
                        data: {
                           id: notificationId
                        },
                        success: function(response) {
                           console.log('Response:', response); // Debugging log to check server response

                           // Check if the response is a valid JSON object and contains 'success'
                           try {
                              var parsedResponse = JSON.parse(response);
                              if (parsedResponse.success) {
                                 // If success, redirect to the report page
                                 window.location.href = '<?php echo site_url('report'); ?>';
                              } else {
                                 // Handle failure to mark as read
                                 console.error('Failed to mark notification as read:', parsedResponse.message);
                              }
                           } catch (e) {
                              console.error('Error parsing response:', e);
                           }
                        },
                        error: function(xhr, status, error) {
                           console.error('AJAX request failed:', error);
                        }
                     });
                  });

                  $(document).on('click', '#hide-notifications', function() {
                     $('#unread-notifications').hide(); // Hide the notifications panel
                  });

               });
            </script>





            <a href="<?= base_url('create_course'); ?>" class="nav-link create-button header-item create-course-nav-link">
               <ion-icon name="create-outline"></ion-icon>
               <span>Create Module</span>
            </a>

            <div class="switch-mode-container">
               <input type="checkbox" id="switch-mode" hidden>
               <label for="switch-mode" class="switch-mode header-item"></label>
            </div>
         </div>
      </nav>
      <!-- NAVBAR -->
   </section>\






   <!----START-MAIN---->

   <main class="main" id="main">

      <div id="global-loading-panel" class="exclude-loading">
         <div class="loader-container">
            <div class="loader-elements">
               <div class="loader"></div>
               <p class="loader-text">Loading... please-hold-on-a-moment</p>
            </div>
         </div>
      </div>

      <!-- Floating Upload Progress Panel -->
      <div id="upload-progress-panel">
         <div class="upload-panel-header">
            <h4 class="upload-panel-title">File Upload Queue</h4>
            <button class="upload-panel-close" onclick="UploadProgressManager.hidePanel()"><ion-icon name="close-outline"></ion-icon></button>
         </div>
         <div class="upload-panel-body" id="upload-progress-list">
            <!-- Upload items will be dynamically added here -->
         </div>
      </div>

      <script>
         document.addEventListener("DOMContentLoaded", function() {
            const text = document.querySelector('.loader-text');
            const textContent = text.textContent;

            // Clear text content
            text.textContent = '';

            // Wrap each letter in a span element
            for (let i = 0; i < textContent.length; i++) {
               const span = document.createElement('span');
               span.textContent = textContent[i];
               text.appendChild(span);
            }

            // Get all spans inside the loader-text element
            const spans = text.querySelectorAll('span');

            let direction = 'forward';

            // Function to start the bold/italic animation
            function animateText() {
               if (direction === 'forward') {
                  for (let i = 0; i < spans.length; i++) {
                     setTimeout(() => {
                        spans[i].classList.add('bold-italic');
                     }, i * 100);
                  }
                  direction = 'reverse';
               } else {
                  for (let i = spans.length - 1; i >= 0; i--) {
                     setTimeout(() => {
                        spans[i].classList.remove('bold-italic');
                     }, (spans.length - 1 - i) * 100);
                  }
                  direction = 'forward';
               }
            }

            // Start the animation
            setInterval(animateText, 1500);
         });
      </script>


      <script>
         $(document).ready(function() {
            $('form').on('submit', function() {
               $('#global-loading-panel').show();
            });

            $('a').on('click', function(e) {
               let href = $(this).attr('href');
               if (href && href !== '#' && href.toLowerCase() !== 'javascript:void(0)') {
                  $('#global-loading-panel').show();
               }
            });

            $(window).on('load', function() {
               $('#global-loading-panel').hide();
            });
         });
      </script>





      <script>
         const allSideMenu = document.querySelectorAll('#sidebar .side-menu.top li a');

         // Function to handle menu item click
         function handleMenuItemClick(item) {
            const li = item.parentElement;
            allSideMenu.forEach(i => {
               i.parentElement.classList.remove('active');
            });
            li.classList.add('active');
         }

         // Function to check if a menu item matches the current page URL
         function checkActiveMenuItem() {
            const currentPageUrl = window.location.href;
            allSideMenu.forEach(item => {
               const menuItemUrl = item.href;
               if (currentPageUrl === menuItemUrl) {
                  handleMenuItemClick(item);
               }
            });
         }

         // Iterate over each menu item and attach click event listener
         allSideMenu.forEach(item => {
            item.addEventListener('click', function() {
               handleMenuItemClick(item);
            });
         });

         // Check the active menu item when the page loads
         checkActiveMenuItem();


         // TOGGLE SIDEBAR
         // TOGGLE SIDEBAR
         const menuBar = document.querySelector('#content nav .bx.bx-menu');
         const sidebar = document.getElementById('sidebar');
         const titleText = document.querySelectorAll('.title-text');
         const mainContent = document.querySelector('main');

         menuBar.addEventListener('click', function() {
            sidebar.classList.toggle('hide');
            titleText.forEach(element => {
               element.classList.toggle('title-hidden');
            });
            mainContent.classList.toggle('main-full');
         });


         const searchButton = document.querySelector('#content nav form .form-input button');
         const searchButtonIcon = document.querySelector('#content nav form .form-input button .bx');
         const searchForm = document.querySelector('#content nav form');

         searchButton.addEventListener('click', function(e) {
            if (window.innerWidth < 576) {
               e.preventDefault();
               searchForm.classList.toggle('show');
               if (searchForm.classList.contains('show')) {
                  searchButtonIcon.classList.replace('bx-search', 'bx-x');
               } else {
                  searchButtonIcon.classList.replace('bx-x', 'bx-search');
               }
            }
         })


         if (window.innerWidth < 768) {
            sidebar.classList.add('hide');
         } else if (window.innerWidth > 576) {
            searchButtonIcon.classList.replace('bx-x', 'bx-search');
            searchForm.classList.remove('show');
         }


         window.addEventListener('resize', function() {
            if (this.innerWidth > 576) {
               searchButtonIcon.classList.replace('bx-x', 'bx-search');
               searchForm.classList.remove('show');
            }
         });

         const switchMode = document.getElementById('switch-mode');

         // Apply the saved theme from localStorage or default to light mode
         function applyTheme() {
            const theme = localStorage.getItem('theme');

            // If no theme is set, default to light mode
            if (theme === 'dark') {
               document.body.classList.add('dark');
               switchMode.checked = true;
            } else {
               document.body.classList.remove('dark');
               switchMode.checked = false;
               localStorage.setItem('theme', 'light'); // Save the default theme as light
            }
         }

         // Load the saved theme from localStorage on page load
         document.addEventListener('DOMContentLoaded', applyTheme);

         // Listen for changes on the switch and save the theme preference
         switchMode.addEventListener('change', function() {
            if (this.checked) {
               document.body.classList.add('dark');
               localStorage.setItem('theme', 'dark');
            } else {
               document.body.classList.remove('dark');
               localStorage.setItem('theme', 'light');
            }

            reloadGoogleCharts(); // Reload the Google Charts script
         });


         function reloadGoogleCharts() {
            const existingScript = document.querySelector('script[src="https://www.gstatic.com/charts/loader.js"]');

            if (existingScript) {
               existingScript.remove(); // Remove the current script
            }

            const newScript = document.createElement('script'); // Create a new script tag
            newScript.src = 'https://www.gstatic.com/charts/loader.js'; // Set the Google Charts source
            document.head.appendChild(newScript); // Append the new script to reload it

            newScript.onload = function() {
               google.charts.load('current', {
                  'packages': ['corechart']
               });

               // Only call drawCharts if its div is present and non-empty
               if (document.getElementById('chart_div') && document.getElementById('chart_div').children.length > 0) {
                  google.charts.setOnLoadCallback(drawCharts); // Redraw all charts
               }

               // Only call drawChart if its div is present and non-empty
               if (document.getElementById('course-progress-graph') && document.getElementById('course-progress-graph').children.length > 0) {
                  google.charts.setOnLoadCallback(drawChart); // Redraw your specific chart
               }
            };
         }
      </script>












      <div class="message-box-container" style="display: none;">
         <h3>Messages</h3>

         <div class="scroll-message-container">
            <div class="search-bar">
               <input type="text" id="search-recipient" placeholder="Search for a recipient...">
            </div>

            <div class="friends-list">
               <h3>Onboarding Employees</h3>
               <ul id="friends-list"></ul>
            </div>

            <div class="recent-messages">
               <h3>Recent Messages</h3>
               <ul id="recent-messages"></ul>
            </div>

            <button class="chat-messagebox" onclick="hideMessageBox()">Hide Message Box</button>
         </div>
      </div>

      <div class="chat-box" id="chat-box" style="display: none;">
         <div class="chatbox-header">
            <div class="icon-circle">
               <img id="chat-friend-image" src="" alt="Friend's Profile Image" width="35" height="35" style="border-radius: 50%; display: none;">
            </div>
            <span id="chat-friend-name"></span>
            <div id="user-status"><span id="status-span"></span></div>
         </div>

         <div class="chat-content" id="chat-content"></div>

         <div class="chat-send">
            <div class="attachments">
               <button><ion-icon name="link-outline"></ion-icon></button>
               <button><ion-icon name="image-outline"></ion-icon></button>
            </div>
            <textarea id="chat-input" placeholder="Type your message..."></textarea>
            <button onclick="sendMessage()"><ion-icon name="send-outline"></ion-icon></button>
         </div>

         <button class="chat-hide" onclick="hideChat()">Hide Chat</button>
      </div>



      <script>
         // Function to hide the .chat-box div
         function hideChat() {
            const chatBox = document.querySelector('.chat-box');
            if (chatBox) {
               chatBox.style.display = 'none';
            }
         }

         // Function to hide the .message-box-container div
         function hideMessageBox() {
            const chatBox = document.querySelector('.chat-box');
            const messageBox = document.querySelector('.message-box-container');
            if (messageBox) {
               messageBox.style.display = 'none';
               chatBox.style.right = '5%';
            }
         }
      </script>

      <script>
         const textarea = document.querySelector('.chat-send textarea');

         textarea.addEventListener('input', function() {
            textarea.style.height = '30px';

            let newHeight = Math.min(textarea.scrollHeight, 120);
            textarea.style.height = newHeight + 'px';
            let radius = 36 - ((newHeight - 30) / 90) * (36 - 12);
            textarea.style.borderRadius = `${radius}px`;
         });
      </script>

      <script>
         const friendList = document.getElementById('friends-list');

         let isDown = false;
         let startX;
         let scrollLeft;

         friendList.addEventListener('mousedown', (e) => {
            isDown = true;
            friendList.classList.add('active');
            startX = e.pageX - friendList.offsetLeft;
            scrollLeft = friendList.scrollLeft;
         });

         friendList.addEventListener('mouseleave', () => {
            isDown = false;
            friendList.classList.remove('active');
         });

         friendList.addEventListener('mouseup', () => {
            isDown = false;
            friendList.classList.remove('active');
         });

         friendList.addEventListener('mousemove', (e) => {
            if (!isDown) return;
            e.preventDefault();
            const x = e.pageX - friendList.offsetLeft;
            const walk = (x - startX) * 2;
            friendList.scrollLeft = scrollLeft - walk;
         });
      </script>




      <script>
         $(document).ready(function() {

            var friendId = null;
            var userId = <?= json_encode($this->session->userdata('id')) ?>;


            if (userId === null) {
               $('#message-icon').hide();
               return;
            }

            $('#message-icon').on('click', function() {
               const chatBox = document.querySelector('.chat-box');
               chatBox.style.right = 'calc(380px + 5% + 12px)';
               $('.message-box-container').toggle();
               $('#unread-count').text('0');
               $('#unread-count').hide();
            });

            loadFriends();
            loadRecentConversations();
            getUnreadCount();

            $('#search-recipient').on('input', function() {
               const query = $(this).val().toLowerCase();

               $.ajax({
                  url: '<?= base_url("MessagesController/searchFriends") ?>',
                  type: 'GET',
                  data: {
                     query: query
                  },
                  success: function(data) {
                     displayFriends(JSON.parse(data));
                  }
               });
            });


            function loadRecentConversations() {
               $.ajax({
                  url: '<?= base_url("MessagesController/getRecentConversations") ?>',
                  type: 'GET',
                  success: function(data) {
                     let conversations = JSON.parse(data);
                     conversations.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                     displayRecentConversations(conversations);
                  },
                  error: function(xhr, status, error) {
                     console.error("Error loading recent conversations:", error);
                  }
               });
            }


            function displayRecentConversations(conversations) {
               $('#recent-messages').empty();
               let displayedFriends = new Set(); // To track unique friend IDs

               conversations.forEach(convo => {
                  // Determine the friend ID
                  let friendId = (convo.sender_id == userId) ? convo.recipient_user_id : convo.sender_id;

                  // Skip adding this friend if they are already in the list
                  if (displayedFriends.has(friendId)) {
                     return;
                  }
                  displayedFriends.add(friendId); // Track this friend as displayed

                  let imageUrl = convo.image_url ? convo.image_url : '<?= base_url("assets/icons/user_placeholder.webp") ?>';
                  let timeAgo = getRelativeTime(convo.created_at);

                  let messageText = convo.message;
                  if (convo.deleted_by_sender == 1) {
                     messageText = 'Message deleted';
                  }

                  $('#recent-messages').append(`
                    <li onclick="selectFriend(${friendId}, '${convo.first_name}', '${imageUrl}')">
                        <span class='img-circle-span'>
                            <img src="${imageUrl}" alt="Profile Image" width="35" height="35" loading="lazy">
                        </span>
                        <div class='span-text-container'>
                            <span class='span-text'>${convo.first_name}</span>
                            <span class='span-message'>${messageText.substring(0, 24)}...</span>
                        </div>
                        <span class='span-time'>${timeAgo}</span>
                    </li>
                `);
               });
            }



            function loadFriends() {
               $.ajax({
                  url: '<?= base_url("MessagesController/getFriends_employee") ?>',
                  type: 'GET',
                  success: function(data) {
                     displayFriends(JSON.parse(data));
                  }
               });
            }

            function displayFriends(friends) {
               $('#friends-list').empty();
               friends.forEach(friend => {
                  let imageUrl = friend.image_url ? friend.image_url.replace('s3://', 'https://YOUR_BUCKET_NAME.s3.amazonaws.com/') : '<?= base_url("assets/icons/user_placeholder.webp") ?>';
                  $('#friends-list').append(`<li onclick="selectFriend(${friend.id}, '${friend.first_name}', '${imageUrl}')">
                    <span class='circle-span'><img src="${imageUrl}" alt="Profile Image" width="35" height="35" loading="lazy"></span>
                    <span class='span-text'>${friend.first_name}</span></li>`);
               });
            }

            window.selectFriend = function(selectedFriendId, friendName, friendImageUrl) {
               friendId = selectedFriendId;

               $('#chat-friend-name').text(friendName).data('friend-id', selectedFriendId);
               $('#chat-friend-image').attr('src', friendImageUrl).show();
               $('#chat-box').show();

               loadChatMessages(friendId);

               $.ajax({
                  url: '<?= base_url("MessagesController/markMessagesAsRead") ?>',
                  type: 'POST',
                  data: {
                     friend_id: friendId
                  },
                  success: function(data) {
                     console.log('Messages marked as read for friend ID:', friendId);
                  },
                  error: function(xhr, status, error) {
                     console.error('Error marking messages as read:', error);
                  }
               });

               startMessagePolling(friendId);
               startStatusInterval();
            };


            function loadChatMessages(friendId) {
               $.ajax({
                  url: '<?= base_url("MessagesController/getMessagesWithFriend") ?>',
                  type: 'POST',
                  data: {
                     friend_id: friendId
                  },
                  success: function(response) {
                     let messages = JSON.parse(response);
                     displayChatMessages(messages);
                  },
                  error: function(xhr, status, error) {
                     console.error('Error loading chat messages:', error);
                  }
               });
            }


            // Add event listener for delete message button
            // Add event listener for delete message button
            $(document).on('click', '.delete-message-button', function() {
               const messageId = $(this).data('message-id');
               // Send AJAX request to update 'deleted_by_sender' column in messages table
               $.ajax({
                  url: '<?= base_url("MessagesController/updateMessage") ?>',
                  type: 'POST',
                  data: {
                     message_id: messageId,
                     deleted_by_sender: 1
                  },
                  success: function(response) {
                     console.log('Message deleted successfully');
                     // Update UI to reflect deleted message
                     $(`[data-message-id="${messageId}"]`).closest('.message').remove();
                     // Load updated chat messages
                     loadChatMessages(friendId);
                  },
                  error: function(xhr, status, error) {
                     console.error('Error deleting message:', error);
                  }
               });
            });



            function displayChatMessages(messages) {
               $('#chat-content').empty();

               messages.forEach(message => {
                  const userId = <?= $this->session->userdata('id') ?>;
                  const messageClass = message.sender_id == userId ? 'sent' : 'received';
                  const messageId = message.id;
                  const messageTime = getRelativeTime(message.created_at);

                  let messageTextHtml = '';
                  if (message.deleted_by_sender == 1) {
                     messageTextHtml = '<div class="message-text message-deleted">Message deleted</div>';
                  } else {
                     messageTextHtml = '<div class="message-text">' + message.message + '</div>';
                     if (messageClass === 'sent') {
                        messageTextHtml += '<button class="delete-message-button" data-message-id="' + messageId + '">Remove</button>';
                     }
                  }

                  $('#chat-content').append(`
            <div class="${messageClass}">
                ${messageTextHtml}
                <span class="message-time">${messageTime}</span>
            </div>
        `);
               });

               adjustBorderRadius();

               // Optionally, scroll the chat box to the bottom when new messages are added
               $('#chat-content').scrollTop($('#chat-content')[0].scrollHeight);
            }



            function adjustBorderRadius() {

               const messages = document.querySelectorAll('.received .message-text, .sent .message-text');

               messages.forEach(message => {
                  const height = message.offsetHeight;

                  if (height > 28) {
                     message.parentElement.classList.add('multi-line');
                  } else {
                     message.parentElement.classList.remove('multi-line');
                  }
               });
            }




            let messagePollingInterval;

            function startMessagePolling(friendId) {
               if (messagePollingInterval) {
                  clearInterval(messagePollingInterval);
               }

               messagePollingInterval = setInterval(function() {
                  $.ajax({
                     url: '<?= base_url("MessagesController/getMessagesWithFriend") ?>',
                     type: 'POST',
                     data: {
                        friend_id: friendId
                     },
                     success: function(response) {
                        let messages = JSON.parse(response);
                        if (!messages || messages.length === 0) {
                           return; // No messages to process
                        }
                        const latestMessage = messages[messages.length - 1];
                        const lastMessageTime = new Date(latestMessage.created_at).getTime();
                        const lastSeenTime = $('#chat-content').data('last-seen-time') || 0;

                        if (lastMessageTime > lastSeenTime) {
                           displayChatMessages(messages);
                           $('#chat-content').data('last-seen-time', lastMessageTime);
                        }
                     },
                     error: function(xhr, status, error) {
                        console.error('Error polling for new messages:', error);
                     }
                  });
               }, 3000);
            }

            window.closeChat = function() {
               clearInterval(messagePollingInterval);
            }

            let isSendingMessage = false;

            window.sendMessage = function() {
               if (isSendingMessage) return;

               isSendingMessage = true;

               const messageText = $('#chat-input').val();
               const friendId = $('#chat-friend-name').data('friend-id');

               if (messageText && friendId) {
                  $.ajax({
                     url: '<?= base_url("MessagesController/sendMessage") ?>',
                     type: 'POST',
                     data: {
                        message: messageText,
                        recipient_user_id: friendId
                     },
                     success: function(response) {
                        $('#chat-input').val('');
                        loadChatMessages(friendId);
                     },
                     error: function(xhr, status, error) {
                        console.error('Error sending message:', error);
                     },
                     complete: function() {
                        isSendingMessage = false;
                     }
                  });
               } else {
                  isSendingMessage = false;
               }
            };


            $('#chat-input').on('keydown', function(e) {
               if (e.which === 13 && !e.shiftKey) {
                  e.preventDefault();
                  window.sendMessage();
               }
            });


            function getRelativeTime(timestamp) {
               const now = new Date();
               const messageTime = new Date(timestamp);
               const diffInSeconds = Math.floor((now - messageTime) / 1000);

               // If message was sent within the last minute
               if (diffInSeconds < 60) {
                  return `${diffInSeconds} sec ago`;
               }

               const diffInMinutes = Math.floor(diffInSeconds / 60);
               if (diffInMinutes < 60) {
                  return `${diffInMinutes} min ago`;
               }

               const diffInHours = Math.floor(diffInMinutes / 60);
               if (diffInHours < 24) {
                  return `${diffInHours} hours ago`;
               }

               const diffInDays = Math.floor(diffInHours / 24);
               if (diffInDays === 1) {
                  return "Yesterday";
               }

               const options = {
                  month: 'long',
                  day: 'numeric'
               };
               return messageTime.toLocaleDateString('en-US', options);
            }


            function getUnreadCount() {
               $.ajax({
                  url: '<?= base_url("MessagesController/getUnreadCount") ?>',
                  type: 'GET',
                  success: function(data) {
                     const unreadCount = parseInt(data);
                     $('#unread-count').text(unreadCount);

                     if (unreadCount > 0) {
                        $('#unread-count').show();
                     } else {
                        $('#unread-count').hide();
                     }
                  },
                  error: function(xhr, status, error) {
                     console.error('Error fetching unread count:', error);
                  }
               });
            }


            function updateStatus() {
               $.ajax({
                  url: '<?php echo site_url("UserStatus/update_status"); ?>',
                  type: 'GET',
                  success: function(response) {
                     console.log(response);
                  }
               });
            }


            function checkUserStatus(friendId) {
               if (friendId) {
                  $.ajax({
                     url: '<?= site_url("UserStatus/check_online/") ?>' + friendId,
                     type: 'GET',
                     success: function(response) {
                        if (response === "User is online") {
                           $('#status-span').text('Online').css('color', 'green');
                        } else if (response === "User is offline") {
                           $('#status-span').text('Offline').css('color', 'red');
                        } else {
                           $('#status-span').text('User not found').css('color', 'gray');
                        }
                     },
                     error: function() {
                        $('#status-span').text('Error checking status').css('color', 'gray');
                     }
                  });
               } else {
                  $('#status-span').text('User not found').css('color', 'gray');
               }
            }


            setInterval(updateStatus, 30000); // 30 seconds

            function startStatusInterval() {
               if (friendId) {
                  setInterval(function() {
                     if (friendId) {
                        checkUserStatus(friendId); // Pass the current friendId
                     }
                  }, 5000); // 5 seconds
               }
            }


         });
      </script>


      <script>
         // Select the #menu-icon div and the .publish-content div
         const menuIconAdmin = document.getElementById('menu-icon');
         const blsIconAdmin = document.getElementById('bls-icon');

         // Track the current width state
         let isSmallWidth_admin = false;

         // Add a click event listener to the #menu-icon div
         menuIconAdmin.addEventListener('click', function() {
            if (isSmallWidth_admin) {
               blsIconAdmin.style.width = '160px';
               blsIconAdmin.style.marginLeft = '0';
            } else {
               blsIconAdmin.style.width = '32px';
               blsIconAdmin.style.marginLeft = '-12px';
            }

            // Toggle the state
            isSmallWidth_admin = !isSmallWidth_admin;
         });
      </script>

      <!-- Upload Progress Manager Script -->
      <script>
         class UploadProgressManager {
            constructor() {
               this.uploads = new Map();
               this.panel = document.getElementById('upload-progress-panel');
               this.list = document.getElementById('upload-progress-list');
               this.autoHideTimeout = null;
            }

            static getInstance() {
               if (!UploadProgressManager.instance) {
                  UploadProgressManager.instance = new UploadProgressManager();
               }
               return UploadProgressManager.instance;
            }

            addUpload(fileId, fileName, fileSize, fileType) {
               const upload = {
                  id: fileId,
                  name: fileName,
                  size: fileSize,
                  type: fileType,
                  progress: 0,
                  status: 'uploading',
                  startTime: Date.now()
               };

               this.uploads.set(fileId, upload);
               this.renderUpload(upload);
               this.showPanel();
               this.clearAutoHide();
            }

            updateProgress(fileId, progress) {
               const upload = this.uploads.get(fileId);
               if (upload) {
                  upload.progress = Math.min(100, Math.max(0, progress));
                  this.updateUploadElement(upload);
               }
            }

            completeUpload(fileId) {
               const upload = this.uploads.get(fileId);
               if (upload) {
                  upload.progress = 100;
                  upload.status = 'completed';
                  this.updateUploadElement(upload);
                  this.scheduleAutoHide();
               }
            }

            errorUpload(fileId, errorMessage = 'Upload failed') {
               const upload = this.uploads.get(fileId);
               if (upload) {
                  upload.status = 'error';
                  upload.errorMessage = errorMessage;
                  this.updateUploadElement(upload);
                  this.scheduleAutoHide();
               }
            }

            removeUpload(fileId) {
               this.uploads.delete(fileId);
               const element = document.getElementById(`upload-item-${fileId}`);
               if (element) {
                  element.remove();
               }

               if (this.uploads.size === 0) {
                  this.hidePanel();
               }
            }

            renderUpload(upload) {
               const item = document.createElement('div');
               item.className = 'upload-item';
               item.id = `upload-item-${upload.id}`;

               const iconClass = this.getFileIconClass(upload.type);
               const formattedSize = this.formatFileSize(upload.size);

               item.innerHTML = `
                  <div class="upload-file-icon ${iconClass}">
                     ${this.getFileIcon(upload.type)}
                  </div>
                  <div class="upload-file-info">
                     <div class="upload-file-name" title="${upload.name}">${upload.name}</div>
                     <div class="upload-file-size">${formattedSize}</div>
                     <div class="upload-progress-bar">
                        <div class="upload-progress-fill" style="width: ${upload.progress}%"></div>
                     </div>
                     <div class="upload-progress-text">${upload.progress}%</div>
                  </div>
                  <div class="upload-status uploading">Uploading</div>
               `;

               this.list.appendChild(item);
            }

            updateUploadElement(upload) {
               const item = document.getElementById(`upload-item-${upload.id}`);
               if (!item) return;

               const progressFill = item.querySelector('.upload-progress-fill');
               const progressText = item.querySelector('.upload-progress-text');
               const status = item.querySelector('.upload-status');

               progressFill.style.width = `${upload.progress}%`;
               progressText.textContent = `${upload.progress.toFixed(3)}%`;

               // Update status and styling based on upload state
               item.className = `upload-item ${upload.status}`;
               progressFill.className = `upload-progress-fill ${upload.status}`;

               switch (upload.status) {
                  case 'completed':
                     status.textContent = 'Completed';
                     status.className = 'upload-status completed';
                     break;
                  case 'error':
                     status.textContent = 'Failed';
                     status.className = 'upload-status error';
                     progressText.textContent = upload.errorMessage || 'Error';
                     break;
                  default:
                     status.textContent = 'Uploading';
                     status.className = 'upload-status uploading';
               }
            }

            getFileIconClass(fileType) {
               if (fileType.startsWith('image/')) return 'image';
               if (fileType.startsWith('video/')) return 'video';
               if (fileType.startsWith('audio/')) return 'audio';
               return 'document';
            }

            getFileIcon(fileType) {
               if (fileType.startsWith('image/')) return '🖼️';
               if (fileType.startsWith('video/')) return '🎥';
               if (fileType.startsWith('audio/')) return '🎵';
               if (fileType === 'application/pdf') return '📄';
               return '📁';
            }

            formatFileSize(bytes) {
               if (bytes === 0) return '0 Bytes';
               const k = 1024;
               const sizes = ['Bytes', 'KB', 'MB', 'GB'];
               const i = Math.floor(Math.log(bytes) / Math.log(k));
               return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            showPanel() {
               this.panel.classList.add('show');
            }

            hidePanel() {
               this.panel.classList.remove('show');
               this.clearAutoHide();
            }

            scheduleAutoHide() {
               this.clearAutoHide();
               // Auto-hide after 5 seconds if all uploads are completed or failed
               const allCompleted = Array.from(this.uploads.values())
                  .every(upload => upload.status === 'completed' || upload.status === 'error');

               if (allCompleted) {
                  this.autoHideTimeout = setTimeout(() => {
                     this.hidePanel();
                  }, 5000);
               }
            }

            clearAutoHide() {
               if (this.autoHideTimeout) {
                  clearTimeout(this.autoHideTimeout);
                  this.autoHideTimeout = null;
               }
            }

            // Static methods for global access
            static addUpload(fileId, fileName, fileSize, fileType) {
               return UploadProgressManager.getInstance().addUpload(fileId, fileName, fileSize, fileType);
            }

            static updateProgress(fileId, progress) {
               return UploadProgressManager.getInstance().updateProgress(fileId, progress);
            }

            static completeUpload(fileId) {
               return UploadProgressManager.getInstance().completeUpload(fileId);
            }

            static errorUpload(fileId, errorMessage) {
               return UploadProgressManager.getInstance().errorUpload(fileId, errorMessage);
            }

            static removeUpload(fileId) {
               return UploadProgressManager.getInstance().removeUpload(fileId);
            }

            static hidePanel() {
               return UploadProgressManager.getInstance().hidePanel();
            }
         }

         // Initialize the upload progress manager
         window.UploadProgressManager = UploadProgressManager;
      </script>
