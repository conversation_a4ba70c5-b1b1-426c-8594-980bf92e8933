<div class="course-detail">
    <div class="small-container">
        <div class="course-content-left">
            <div id="video-container" class="video-container-class">


            <?php if (empty($course->image_url)) { ?>
                <!-- Display default image if image_url is empty -->
                <img src="<?php echo base_url('assets/images/onboarding_img.webp'); ?>" alt="Onboarding" loading="lazy">
            <?php } else { ?>
                <!-- Display the actual image if image_url is available -->
                <img src="<?php echo htmlspecialchars($course->image_url, ENT_QUOTES, 'UTF-8'); ?>" alt="Course Image" loading="lazy">
            <?php } ?>


                <div class="player-content"> 
                    <h3><?php echo htmlspecialchars($course->course_name, ENT_QUOTES, 'UTF-8'); ?></h3>
                    <p><?php echo htmlspecialchars($course->designation, ENT_QUOTES, 'UTF-8'); ?></p>
                    <button id="course-action"></button>
                </div>
            </div>
        </div>

        <div class="course-content-right">
            <div class="course-content">
                <h4>Module Components</h4>
            </div>
            <div class="course-content-details">

                <?php echo form_open(); ?>

                <?php $user_id = $this->session->userdata('id'); ?>
                <input type="hidden" id="module_id" value="<?php echo htmlspecialchars($course->module_id, ENT_QUOTES, 'UTF-8'); ?>">
                <input type="hidden" id="user_id" value="<?php echo isset($user_id) ? htmlspecialchars($user_id, ENT_QUOTES, 'UTF-8') : 'none'; ?>">
                <input type="hidden" id="total_duration" name="total_duration" value="">


                <div class="content-details">
                    <?php if (!empty($sections)): ?>
                        <ul id="section-list">
                        <?php foreach ($sections as $index => $section): ?>
                            <li>
                                <div class="section-header" onclick="toggleSectionContent('<?php echo htmlspecialchars($section->section_id ?? '', ENT_QUOTES, 'UTF-8'); ?>')">
                                    <h4><?php echo htmlspecialchars($section->section_name ?? 'Untitled Section', ENT_QUOTES, 'UTF-8'); ?></h4>
                                </div>
                                
                                <div id="section-content-<?php echo htmlspecialchars($section->section_id ?? '', ENT_QUOTES, 'UTF-8'); ?>" class="section-content" <?php if ($index === 0) echo 'style="display:block;"'; ?>>
                                    <?php if (!empty($section->components)): ?>
                                        <?php $componentDifficulty = $section->difficulty / count($section->components); ?>
                                        <ul>
                                            <?php foreach ($section->components as $component): ?>
                                                <li>
                                                    <button type="button" class="component-button" 
                                                        data-type="<?php echo htmlspecialchars($component->type ?? '', ENT_QUOTES, 'UTF-8'); ?>" 
                                                        data-section_id="<?php echo htmlspecialchars($section->section_id ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-section_name="<?php echo htmlspecialchars($section->section_name ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-sequence_num="<?php echo htmlspecialchars($component->sequence_num ?? '', ENT_QUOTES, 'UTF-8'); ?>"

                                                        data-short_reading_title="<?php echo htmlspecialchars($component->short_reading_title ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-short_reading_subtitle="<?php echo htmlspecialchars($component->short_reading_subtitle ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-short_reading_content="<?php echo htmlspecialchars($component->short_reading_content ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-short_reading_url="<?php echo htmlspecialchars($component->short_reading_url ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-short_reading_file="<?php echo htmlspecialchars($component->short_reading_file ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-short_reading_360p="<?php echo htmlspecialchars($component->video_url_360p ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-short_reading_720p="<?php echo htmlspecialchars($component->video_url_720p ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-short_reading_1080p="<?php echo htmlspecialchars($component->video_url_1080p ?? '', ENT_QUOTES, 'UTF-8'); ?>"

                                                        data-short_video_title="<?php echo htmlspecialchars($component->short_video_title ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-short_video_subtitle="<?php echo htmlspecialchars($component->short_video_subtitle ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-short_video_content="<?php echo htmlspecialchars($component->short_video_content ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-short_video_url="<?php echo htmlspecialchars($component->short_video_url ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-short_video_file="<?php echo htmlspecialchars($component->short_video_file ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-short_video_360p="<?php echo htmlspecialchars($component->video_url_360p ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-short_video_720p="<?php echo htmlspecialchars($component->video_url_720p ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-short_video_1080p="<?php echo htmlspecialchars($component->video_url_1080p ?? '', ENT_QUOTES, 'UTF-8'); ?>"

                                                        data-lecture_cast_title="<?php echo htmlspecialchars($component->lecture_cast_title ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-lecture_cast_subtitle="<?php echo htmlspecialchars($component->lecture_cast_subtitle ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-lecture_cast_content="<?php echo htmlspecialchars($component->lecture_cast_content ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-lecture_cast_url="<?php echo htmlspecialchars($component->lecture_cast_url ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-lecture_cast_file="<?php echo htmlspecialchars($component->lecture_cast_file ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-lecture_cast_360p="<?php echo htmlspecialchars($component->video_url_360p ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-lecture_cast_720p="<?php echo htmlspecialchars($component->video_url_720p ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-lecture_cast_1080p="<?php echo htmlspecialchars($component->video_url_1080p ?? '', ENT_QUOTES, 'UTF-8'); ?>"

                                                        data-podcast_title="<?php echo htmlspecialchars($component->podcast_title ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-podcast_subtitle="<?php echo htmlspecialchars($component->podcast_subtitle ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-podcast_content="<?php echo htmlspecialchars($component->podcast_content ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-podcast_url="<?php echo htmlspecialchars($component->podcast_url ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-podcast_file="<?php echo htmlspecialchars($component->podcast_file ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-podcast_360p="<?php echo htmlspecialchars($component->video_url_360p ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-podcast_720p="<?php echo htmlspecialchars($component->video_url_720p ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-podcast_1080p="<?php echo htmlspecialchars($component->video_url_1080p ?? '', ENT_QUOTES, 'UTF-8'); ?>"

                                                        data-reflective_writing_title="<?php echo htmlspecialchars($component->reflective_writing_title ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-reflective_file_url="<?php echo htmlspecialchars($component->reflective_file ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-reflective_writing_question="<?php echo htmlspecialchars($component->reflective_writing_question ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-reflective_writing_limit="<?php echo htmlspecialchars($component->reflective_writing_limit ?? '', ENT_QUOTES, 'UTF-8'); ?>"

                                                        data-short_assessment_question="<?php echo htmlspecialchars($component->short_assessment_question ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-correct_answer="<?php echo htmlspecialchars($component->correct_answer ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-choices="<?php echo htmlspecialchars(json_encode($component->choices ?? []), ENT_QUOTES, 'UTF-8'); ?>"

                                                        data-type="<?php echo htmlspecialchars($component->type ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-multiple_choice_set_title="<?php echo htmlspecialchars($component->set_title ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-multiple_choice_set_questions="<?php echo htmlspecialchars(json_encode($component->questions ?? []), ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-multiple_choice_set_answers="<?php echo htmlspecialchars(json_encode($component->answers ?? []), ENT_QUOTES, 'UTF-8'); ?>"
                                                        data-question_id="<?php echo htmlspecialchars(json_encode($component->question_id ?? []), ENT_QUOTES, 'UTF-8'); ?>"
                                                        >


                                                        <!-- Render the component based on type -->
                                                        <?php if ($component->type === 'short_reading'): ?>
                                                            <input type="checkbox" aria-label="default" class="component-checkbox" 
                                                            data-sequence="<?php echo htmlspecialchars($component->sequence_num ?? 'No Short Reading', ENT_QUOTES, 'UTF-8'); ?>" 
                                                            data-section-id="<?php echo htmlspecialchars($section->section_id ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                            data-difficulty="<?php echo $componentDifficulty; ?>"
                                                            >
                                                            <span class="show-lesson">Short Reading: <?php echo htmlspecialchars($component->short_reading_title ?? 'No Short Reading', ENT_QUOTES, 'UTF-8'); ?></span>
                                                    

                                                        <?php elseif ($component->type === 'short_video'): ?>
                                                            <input type="checkbox" aria-label="default" class="component-checkbox" 
                                                            data-sequence="<?php echo htmlspecialchars($component->sequence_num ?? 'No Video', ENT_QUOTES, 'UTF-8'); ?>" 
                                                            data-section-id="<?php echo htmlspecialchars($section->section_id ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                            data-difficulty="<?php echo $componentDifficulty; ?>"
                                                            >
                                                            <span class="show-lesson">Short Video: <?php echo htmlspecialchars($component->short_video_title ?? 'No Short Reading', ENT_QUOTES, 'UTF-8'); ?></span>

                                                            
                                                        <?php elseif ($component->type === 'lecture_cast'): ?>
                                                            <input type="checkbox" aria-label="default" class="component-checkbox" 
                                                            data-sequence="<?php echo htmlspecialchars($component->sequence_num ?? 'No Lecture Cast', ENT_QUOTES, 'UTF-8'); ?>" 
                                                            data-section-id="<?php echo htmlspecialchars($section->section_id ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                            data-difficulty="<?php echo $componentDifficulty; ?>"
                                                            >
                                                            <span class="show-lesson">Lecture Cast: <?php echo htmlspecialchars($component->lecture_cast_title ?? 'Lecture Cast', ENT_QUOTES, 'UTF-8'); ?></span>

                                                        <?php elseif ($component->type === 'podcast'): ?>
                                                            <input type="checkbox" aria-label="default" class="component-checkbox" 
                                                            data-sequence="<?php echo htmlspecialchars($component->sequence_num ?? 'No Podcast', ENT_QUOTES, 'UTF-8'); ?>" 
                                                            data-section-id="<?php echo htmlspecialchars($section->section_id ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                            data-difficulty="<?php echo $componentDifficulty; ?>"
                                                            >
                                                            <span class="show-lesson">Podcast: <?php echo htmlspecialchars($component->podcast_title ?? 'Podcast', ENT_QUOTES, 'UTF-8'); ?></span>

                                                        <?php elseif ($component->type === 'reflective_writing'): ?>
                                                            <input type="checkbox" aria-label="default" class="component-checkbox" 
                                                            data-sequence="<?php echo htmlspecialchars($component->sequence_num ?? 'No Reflective Writing', ENT_QUOTES, 'UTF-8'); ?>" 
                                                            data-section-id="<?php echo htmlspecialchars($section->section_id ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                            data-difficulty="<?php echo $componentDifficulty; ?>"
                                                            >
                                                            <span class="show-lesson">Reflective Writing: <?php echo htmlspecialchars($component->reflective_writing_title ?? 'Podcast', ENT_QUOTES, 'UTF-8'); ?></span>

                                                        <?php elseif ($component->type === 'short_assessment'): ?>
                                                            <input type="checkbox" aria-label="default" class="component-checkbox" 
                                                            data-sequence="<?php echo htmlspecialchars($component->sequence_num ?? 'No Assessment', ENT_QUOTES, 'UTF-8'); ?>" 
                                                            data-section-id="<?php echo htmlspecialchars($section->section_id ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                            data-difficulty="<?php echo $componentDifficulty; ?>"
                                                            >
                                                            <span class="show-lesson">Short Assessment: <?php echo htmlspecialchars($component->short_assessment_title ?? 'No Assessment', ENT_QUOTES, 'UTF-8'); ?></span>
                                                        
                                                        <?php elseif ($component->type === 'multiple_choice_set'): ?>
                                                            <input type="checkbox" aria-label="default" class="component-checkbox" 
                                                            data-sequence="<?php echo htmlspecialchars($component->sequence_num ?? 'No Multiple Choice Set', ENT_QUOTES, 'UTF-8'); ?>" 
                                                            data-section-id="<?php echo htmlspecialchars($section->section_id ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                                            data-difficulty="<?php echo $componentDifficulty; ?>"
                                                            >       
                                                            <span class="show-lesson">Multiple Choice Set: <?php echo htmlspecialchars($component->set_title ?? 'No Multiple Choice Set', ENT_QUOTES, 'UTF-8'); ?></span>
                                                        <?php endif; ?>

                                                    </button>
                                                </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        <?php endif; ?>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
     
                    <input type="hidden" name="module_id" value="<?php echo htmlspecialchars($course->module_id, ENT_QUOTES, 'UTF-8'); ?>">

                    <?php else: ?>
                        <p>No sections available for this course.</p>
                    <?php endif; ?>

                </div>

                <?php echo form_close(); ?>
            </div>
        </div>
    </div>






    <div class="progress-main-container">
        <div class="numeric-progress">
            <p id="progress-text">0.00%</p>
            <p>Module Completion</p>
        </div>
        <div class="progress-container">
            <div class="progress-bar" id="progress-bar"></div>
        </div>
    </div>

    <div class="small-container course-header-main">
        <dv class="course-header-left">
            <div class="left-resources">
                <div class="progress-item">
                    <button type="button">Download Resources</button>
                </div>
                <div class="progress-item">
                    <button type="button">Download Transcript</button>
                </div>
            </div>
        </dv>

        <div class="course-header-right">
            <button type="button" id="skip-and-proceed" class="count-component">Complete and Continue</button>
            <button type="submit" id="save-progress" class="save-progress">Save Progress</button>
            <p class="save-progress-text">or Press CTRL + S</p>
            <div id="popup-progress-saved" class="progress-success"></div>
            <div id="popup-progress-updated" class="progress-success"></div>
        </div>
    </div>

    <div class="course-full-details">
        <div class="small-container">
            <div class="detail">

                <div class="detail-item">
                    <?php if (empty($course->image_url)) { ?>
                    <!-- Display default image if image_url is empty -->
                    <img src="<?php echo base_url('assets/images/onboarding_img.webp'); ?>" alt="Onboarding" loading="lazy">
                    <?php } else { ?>
                        <!-- Display the actual image if image_url is available -->
                        <img src="<?php echo htmlspecialchars($course->image_url, ENT_QUOTES, 'UTF-8'); ?>" alt="Course Image" loading="lazy">
                    <?php } ?>
                </div>

                <div class="detail-item">
                    <h3><?php echo htmlspecialchars($course->course_name, ENT_QUOTES, 'UTF-8'); ?></h3>
                    <p class="description"><?php echo htmlspecialchars($course->designation, ENT_QUOTES, 'UTF-8'); ?></p>
                    <p class="description"><?php echo htmlspecialchars($course->category, ENT_QUOTES, 'UTF-8'); ?></p>
                    <p class="description"><?php echo htmlspecialchars($course->description, ENT_QUOTES, 'UTF-8'); ?></p>
                </div>

                <div class="detail-item">
                    <h3>Why Take This Module?</h3>
                    <ul>
                        <li>Enhance your skills to improve job performance</li>
                        <li>Learn at your own pace with flexible access</li>
                        <li>Earn a certificate upon passing</li>
                        <li>Access course materials anytime for future reference</li>
                    </ul>
                    <a href="<?php echo base_url('courses'); ?>" class="btn btn-primary">
                        Check All Modules
                    </a>
                </div>


            </div>
        </div> 
    </div>







<script>
    // Wait for the DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Get the save progress button
        var saveButton = document.getElementById('save-progress');
        var progressSaved = document.getElementById('popup-progress-saved');
        var progressUpdated = document.getElementById('popup-progress-updated');


    // Add an event listener to the save progress button
    saveButton.addEventListener('click', function(event) {
        // Prevent the default form submission behavior
        event.preventDefault();

        // Get all checked boxes
        var checkedBoxes = document.querySelectorAll('input[type="checkbox"]:checked');

        // Check if there are any checked boxes
        if (checkedBoxes.length === 0) {
            SmartAlerts.warning('Please select at least one component to proceed with the course.', 'Selection Required');
            return;
        }

        // Get the last checked box
        var lastCheckedBox = checkedBoxes[checkedBoxes.length - 1];

        // Get the user_id, module_id, and sequence_num
        var userId = document.getElementById('user_id').value;
        var moduleId = document.getElementById('module_id').value;
        var sequenceNum = lastCheckedBox.dataset.sequence;

        // Get the current date
        var currentDate = new Date();
        var formattedDate = currentDate.toISOString().split('T')[0];

        // Get the current progress text
        var progressText = document.getElementById('progress-text').textContent;

        // Check if there is already a record in the tbl_progress table
        var xhr = new XMLHttpRequest();
        xhr.open('POST', '<?php echo base_url('progress/check_progress'); ?>', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.send('user_id=' + userId + '&module_id=' + moduleId);

        xhr.onload = function() {
            if (xhr.status === 200) {
                var response = JSON.parse(xhr.responseText);
                if (response.exists) {
                    // Update the sequence_num and progress in the tbl_progress table
                    var updateXhr = new XMLHttpRequest();
                    updateXhr.open('POST', '<?php echo base_url('progress/update_progress'); ?>', true);
                    updateXhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                    updateXhr.send('user_id=' + userId + '&module_id=' + moduleId + '&sequence_num=' + sequenceNum + '&progress=' + progressText);

                    updateXhr.onload = function() {
                        if (updateXhr.status === 200) {
                            console.log('Progress updated successfully');
                            progressUpdated.textContent = 'Progress Updated Successfully';
                            progressUpdated.style.padding = '8px 24px';
                            progressUpdated.style.display = 'block';
                            progressUpdated.style.opacity = '1';
                            progressUpdated.style.fontFamily = 'Open Sans, sans-serif';
                            
                            setTimeout(function() {
                                progressUpdated.style.display = 'none';
                                progressUpdated.textContent = '';
                                progressUpdated.style.padding = '0px';
                                progressUpdated.style.opacity = '0';
                            }, 4000);
                        } 
                        
                        else {
                            console.log('Error updating progress');
                        }
                    }
                } 
                else {
                    // Insert a new record into the tbl_progress table
                    var insertXhr = new XMLHttpRequest();
                    insertXhr.open('POST', '<?php echo base_url('progress/insert_progress'); ?>', true);
                    insertXhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                    insertXhr.send('user_id=' + userId + '&module_id=' + moduleId + '&date=' + formattedDate + '&sequence_num=' + sequenceNum + '&progress=' + progressText);

                    insertXhr.onload = function() {
                        if (insertXhr.status === 200) {
                            console.log('Progress inserted successfully');
                            progressSaved.textContent = 'Progress Saved Successfully';
                            progressSaved.style.padding = '8px 24px';
                            progressSaved.style.display = 'block';

                            setTimeout(function() {
                                progressUpdated.style.display = 'none';
                                progressSaved.textContent = '';
                            }, 4000);

                        } 
                        else {
                            console.log('Error inserting progress');
                        }
                    };
                }
            } 
            else {
                console.log('Error checking progress');
            }
        };
    });
});
</script>




<script>
    var baseUrl = "<?php echo base_url(); ?>";
</script>

<script src="<?php echo base_url('assets/js/course_detail.js'); ?>"></script>
<script src="<?php echo base_url('assets/js/video-quality-manager.js'); ?>"></script>
<script src="<?php echo base_url('assets/js/course_detail_component.js'); ?>"></script>
<script src="<?php echo base_url('assets/js/course_detail_sequence.js'); ?>"></script>



<script>
    var terminate = false;
    var user_id = '<?php echo isset($user_id) ? htmlspecialchars($user_id, ENT_QUOTES, 'UTF-8') : 'none'; ?>';
    var module_id = '<?php echo htmlspecialchars($course->module_id, ENT_QUOTES, 'UTF-8'); ?>';

    function getProgress() {
        if (terminate) return;
        $.ajax({
            type: 'POST',
            url: '<?php echo site_url('progress_ajax/get_progress_data'); ?>',
            data: {user_id: user_id, module_id: module_id},
            dataType: 'json',
            success: function(data) {
                var sequence_num = data.sequence_num;
                var progress = data.progress;

  
                if (sequence_num === null || progress === null) {
                    terminate = true;
                }
                else {
                    terminate = false;
                }
            }
        });
    }
    getProgress();
</script>


<script>
    // Get the button element by its ID
    const saveButton = document.getElementById('save-progress');

    // Listen for keydown event on the document
    document.addEventListener('keydown', function(event) {
      // Check if the Ctrl key and the 'S' key are pressed simultaneously
      if (event.ctrlKey && event.key === 's') {
        event.preventDefault();  // Prevent the default "Save As" dialog in the browser
        saveButton.click();      // Trigger the button click
      }
    });

  </script>

