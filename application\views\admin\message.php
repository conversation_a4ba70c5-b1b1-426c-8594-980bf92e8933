<div class="main-container">
    <h4 class="message-title">Messages</h4>
    <div class="message-main-container">

        <div class="recent-main-panel message-main-item" data-aos="fade-right">
            <div class="recent-header">
                <h3>Recent Messages</h3>
            </div>
          
            <div class="form-group">
                <input type="text" class="form-control" id="search_msg" placeholder="Search recent message">
            </div>
            <div id="recent-msg-main"></div>
        </div>

        <form class="message-main-item bulk-message exclude-loading" action="<?php echo site_url('admin/send_message'); ?>" method="post" data-aos="fade-left">

        <h3>Bulk Message</h3>
        <div class="message-header">

            <div class="bulk-filter">
  

                <div class="bulk-item">
                    <select id="department" name="department" aria-label="Select Department">
                        <option value="">All Departments</option>
                    </select>
                </div>

                <div class="bulk-item">
                    <select id="employee_status" name="employee_status" aria-label="Select Status">
                        <option value="">All Statuses</option>
                    </select>
                </div>

            </div>
        </div>

        <div class="recipient-list">
            <div class="recipient-list-header">
                <h3>Selected Recipients</h3>
                <button type="button" class="clear-selection" id="bulk-remove-recipients">Clear Selection</button>
            </div>

            <ul id="selectedRecipients">
                
            </ul>
        </div>

        <div class="form-group message-textarea">
            <textarea class="form-control" id="message" name="message" rows="3" required contenteditable="false" placeholder="Type your message here..."></textarea>
            <div class="attachment-container">
                <button type="button"><ion-icon name="document-outline"></ion-icon>File Attachment</button>
                <button type="button"><ion-icon name="images-outline"></ion-icon>Image Attachment</button>
            </div>
        </div>

        <input type="hidden" value="<?php echo $this->session->userdata('id'); ?>" name="sender">
        <button type="submit" class="btn btn-primary bulk-message-submit"><ion-icon name="send-outline"></ion-icon>Send Message</button>

        <!-- Hidden div to append removed recipients -->
        <div id="removedRecipients" style="display:none;"></div>

        </form>

    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script type="text/javascript">
$(document).ready(function() {
    // Fetch and populate departments
    $.ajax({
        url: "<?php echo site_url('admin/get_departments_for_message'); ?>",
        method: "GET",
        dataType: "json",
        success: function(data) {
            $('#department').empty().append('<option value="">All Business Unit</option>');
            data.forEach(department => {
                $('#department').append(`<option value="${department.department}">${department.department}</option>`);
            });
        }
    });

    // Fetch and populate employee statuses
    $.ajax({
        url: "<?php echo site_url('admin/get_statuses_for_message'); ?>",
        method: "GET",
        dataType: "json",
        success: function(data) {
            $('#employee_status').empty().append('<option value="">Select All Statuses</option>');
            data.forEach(status => {
                $('#employee_status').append(`<option value="${status.employee_status}">${status.employee_status}</option>`);
            });
        }
    });

    // Function to fetch and display users based on filters
    const fetchAndUpdateRecipients = () => {
        const department = $('#department').val();
        const status = $('#employee_status').val();

        $.ajax({
            url: "<?php echo site_url('admin/get_filtered_users'); ?>",
            method: "GET",
            data: { department, status },
            dataType: "json",
            success: function(users) {
                $('#selectedRecipients').empty();
                users.forEach(user => {
                    $('#selectedRecipients').append(`
                        <li data-id="${user.id}">
                            ${user.first_name} ${user.last_name}
                            <button type="button" class="remove-recipient btn btn-danger btn-sm"><ion-icon name="trash-outline"></ion-icon></button>
                        </li>
                    `);
                });
            }
        });
    };

    // Trigger recipient update on department or status change
    $('#department, #employee_status').change(fetchAndUpdateRecipients);

    // Remove recipient and add hidden input for removal
    $(document).on('click', '.remove-recipient', function() {
        const userId = $(this).closest('li').data('id');
        
        // Remove recipient from the list
        $(this).closest('li').remove();

        // Create hidden input for the removed recipient
        const hiddenInput = $('<input>', {
            type: 'hidden',
            name: 'removed_recipients[]',
            value: userId
        });

        // Append the hidden input to the form
        $('#removedRecipients').append(hiddenInput);
    });

    // Bulk remove all recipients
    $('#bulk-remove-recipients').on('click', function() {
        $('#selectedRecipients').empty(); // Clear all recipients
        $('#removedRecipients').empty(); // Clear all hidden inputs
    });

    // Form submission handler
    $('form').on('submit', function(e) {
        const recipientIds = [];
        $('#selectedRecipients li').each(function() {
            const userId = $(this).data('id');
            recipientIds.push(userId);
        });

        // Check if there are no recipients selected
        if (recipientIds.length === 0) {
            e.preventDefault(); // Prevent form submission
            SmartAlerts.warning("Please select at least one recipient before sending the message.", "Recipients Required");
            location.reload(); // Reload the page to reflect changes
            return false; // Ensure that the form doesn't submit without recipients
        }

        // Create a hidden input for recipient IDs
        $('<input>').attr('type', 'hidden').attr('name', 'recipient_ids').attr('value', recipientIds.join(',')).appendTo('form');
    });

});
</script>








<script>
    $(document).ready(function() {

        var friendId = null;
        var userId = <?= json_encode($this->session->userdata('id')) ?>;

        loadRecentConversations();

        // Hold conversations globally to filter on search
        let allConversations = [];

        function loadRecentConversations() {
            $.ajax({
                url: '<?= base_url("MessagesController/getRecentConversations") ?>',
                type: 'GET',
                success: function(data) {
                    allConversations = JSON.parse(data);
                    allConversations.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                    displayRecentConversations(allConversations);
                },
                error: function(xhr, status, error) {
                    console.error("Error loading recent conversations:", error);
                }
            });
        }

        function displayRecentConversations(conversations) {
            $('#recent-msg-main').empty();

            let displayedFriends = new Set(); // To track unique friend IDs
            const limit = 10; // Number of items to show per chunk
            let start = 0; // Start index for displaying conversations

            // Helper function to render a chunk of conversations
            function renderChunk() {
                let addedCount = 0; // Track how many items are added

                for (let i = start; i < conversations.length; i++) {
                    const convo = conversations[i];
                    // Determine the friend ID
                    let friendId = (convo.sender_id == userId) ? convo.recipient_user_id : convo.sender_id;

                    // Skip adding this friend if they are already in the list
                    if (displayedFriends.has(friendId)) {
                        continue;
                    }

                    displayedFriends.add(friendId); // Track this friend as displayed

                    let imageUrl = convo.image_url
                        ? convo.image_url
                        : '<?= base_url("assets/icons/user_placeholder.webp") ?>';
                    let timeAgo = getRelativeTime(convo.created_at);

                    let messageText = convo.message;
                    if (convo.deleted_by_sender == 1) {
                        messageText = 'Message deleted';
                    }

                    $('#recent-msg-main').append(`
                        <div class="recent-message-container" onclick="selectFriend(${friendId}, '${convo.first_name}', '${imageUrl}')">
                            <img class='rmc-item' src="${imageUrl}" alt="Profile Image" width="32px" height="32px" loading="lazy">
                           <span class='span-text rmc-item'>${convo.first_name} ${convo.last_name}</span>
                            <span class='span-message rmc-item'>${messageText.substring(0, 24)}</span>
                            <span class='span-time rmc-item'>${timeAgo}</span>
                        </div>
                    `);

                    addedCount++;
                    if (addedCount >= limit) {
                        start = i + 1; // Update the starting index for the next chunk
                        break;
                    }
                }

                // If all conversations are loaded, remove the "Load More" button
                if (start >= conversations.length) {
                    $('#load-more-btn').remove(); // Completely remove the button
                } else {
                    // If there are more items to load and the button doesn't exist, create it
                    if ($('#load-more-btn').length === 0) {
                        $('#recent-msg-main').after(`
                            <button type="button" id="load-more-btn">
                                <ion-icon name="reload-outline"></ion-icon>
                                More Conversations
                            </button>
                        `);
                    }
                }
            }

            // Initial render
            renderChunk();

            // Event listener for the "Load More" button
            $(document).off('click', '#load-more-btn').on('click', '#load-more-btn', function () {
                renderChunk();
            });
        }


        

        function getRelativeTime(timestamp) {  
            const now = new Date();  
            const messageTime = new Date(timestamp);
            const diffInSeconds = Math.floor((now - messageTime) / 1000);

            if (diffInSeconds < 60) {
                return diffInSeconds === 1 ? "1 second" : `${diffInSeconds} seconds`;
            }

            const diffInMinutes = Math.floor(diffInSeconds / 60);
            if (diffInMinutes < 60) {
                return diffInMinutes === 1 ? "1 minute" : `${diffInMinutes} minutes`;
            }

            const diffInHours = Math.floor(diffInMinutes / 60);
            if (diffInHours < 24) {
                return diffInHours === 1 ? "1 hour" : `${diffInHours} hours`;
            }

            const diffInDays = Math.floor(diffInHours / 24);
            if (diffInDays === 1) {
                return "Yesterday";
            }

            if (diffInDays < 30) {
                return `${diffInDays} days`;
            }

            const diffInMonths = Math.floor(diffInDays / 30);
            if (diffInMonths < 12) {
                return diffInMonths === 1 ? "1 month" : `${diffInMonths} months`;
            }

            const diffInYears = Math.floor(diffInMonths / 12);
            return diffInYears === 1 ? "1 year" : `${diffInYears} years`;
        }



        // Search functionality
        $('#search_msg').on('input', function() {
            const searchQuery = $(this).val().toLowerCase();
            
            // Filter conversations by recipient's first name
            const filteredConversations = allConversations.filter(convo => {
                return convo.first_name.toLowerCase().includes(searchQuery);
            });

            displayRecentConversations(filteredConversations);
        });
    });
</script>


<script>
    AOS.init();
</script>
