* {
	margin: 0;
	padding: 0;
	list-style-type: none;
	box-sizing: border-box;
	text-decoration: none;
}
body::-webkit-scrollbar {
	width: 7px;
	background: transparent;
	border-radius: 12px;
	height: 0;
}
body::-webkit-scrollbar-thumb {
	background-color: #a8a8a8 !important;
	border-radius: 12px !important;
}
body::-webkit-scrollbar-track {
	background-color: transparent;
	border-radius: 12px;
}
h1,
h2,
h3,
h4,
h5,
h6 {
	font-family: "Inter", serif;
	font-weight: Bold;
}
p,
small,
span,
input,
textarea,
select,
a,
button,
li,
label {
	font-family: "Open Sans", sans-serif;
	font-size: small;
}
.serif-style {
   display: inline-block;
   font-family: 'Playfair Display' !important;
   font-style: italic;
   font-size: 3rem;
}


/* public/css/animations.css
.fade-in {
    animation: fadeIn 1s forwards;
  }
  
  .fade-out {
    animation: fadeOut 1s forwards;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes fadeOut {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }
   */

:root {
	--light: #ffffff;
	--dark: #1e1e1e;
	--grey: #4e4e4e;
	--red: #ff0000;
	--btn-blue: #0741e0;
	--btn-blue: #db1313;
	--gold: #ffca1d;
	--list: #f1f4f8;
}
body {
	background: var(--light);
}
button {
	cursor: pointer;
}
.header-margin {
	height: 10vh;
}
header {
	height: 10vh;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: fixed;
	top: 0;
	width: 100%;
	z-index: 99999;
	margin-bottom: 10vh;
	background: var(--light);
}
.small-container {
	width: 90%;
	margin: 0 auto;
}
header .small-container {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
header .header-item {
	display: flex;
	align-items: center;
}
header .header-item .img-base-url {
	width: 160px;
	display: block;
	margin-right: 42px;
	transform: translateY(-4px);
}
.header-item img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

header .small-container .header-item:nth-child(2) {
	flex: 1;
	margin-left: 56px;
	margin-right: 56px;
}
header .header-item:first-child {
	justify-content: start;
}

header button {
	border: none;
	background: none;
	font-weight: 500;
	font-size: small;
	transition: all 0.2s ease-in-out;
}

.header-nav-item:not(:last-child) {
	margin-right: 16px;
}
.header-nav-item a {
	font-size: small;
	padding: 4px 8px;
	color: var(--dark);
	font-weight: 500;
	white-space: nowrap;
}
.header-nav-item a:hover {
	text-decoration: underline;
}

.bx-notification,
.bx-message {
	margin: 0 !important;
	padding: 0 !important;
}
.notification {
	border: 1px solid #ccc;
	position: fixed;
	top: calc(10vh + 12px);
	width: 380px;
	right: calc(5% + 12px);
	padding: 12px;
	z-index: 9999;
	background: var(--light);
	border-radius: 12px;
	color: var(--dark);
	font-size: small;
}

.notification-header {
	margin-bottom: 12px;
	font-weight: bold;
	font-size: small;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.notification-list ul {
	flex-direction: column;
	overflow-y: scroll;
	height: 240px;
	justify-content: flex-start !important;
}

.notification-list ul::-webkit-scrollbar {
	width: 0;
	height: 0;
}
.notification-list ul li {
	margin-left: 0 !important;
	margin-bottom: 6px;
	border-radius: 8px;
	background: rgb(240, 244, 255);
	padding: 6px 12px;
	width: 100%;
}
.notification-list ul li a {
	color: var(--dark);
}
.notification-list ul li a span:last-child {
	display: block;
	width: max-content;
	margin-top: 12px;
	font-size: small;
}
.close-notification {
	padding: 2px 8px;
	padding-bottom: 3px;
	font-size: 12px;
	border-radius: 36px;
	border: none;
	background: #f3f3f3;
	color: var(--dark);
	transition: all 0.2s ease-in-out;
}
.close-notification:hover {
	color: white;
	background: rgb(255, 89, 89);
}

.header-nav-item img {
	width: 70px;
	height: 100%;
	object-fit: cover;
}

header .header-item:last-child {
	justify-content: end;
}
.message-notify {
	align-items: center;
	display: flex;
}
.message-notify button {
	display: flex;
	position: relative;
}
.message-notify button span {
	background: rgb(230, 5, 5);
	color: var(--light);
	font-size: 10px;
	width: 14px;
	height: 14px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
	right: -6px;
	top: -6px;
	padding: 8px;
}
.message-notify button:first-child {
	margin-right: 24px;
}
.message-notify i {
	font-size: 16px;
	margin-right: 0 !important;
}
.list-menu {
	display: none;
}

.header-item ul {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.header-item ul li:not(:first-child) {
	margin-left: 24px;
}

.header-item ul li i {
	display: flex;
	align-items: center;
	color: var(--dark);
	margin-right: 8px;
}
.header-item ul li {
	font-size: small;
}

.menu {
	display: none;
}
.hidden {
	display: none;
}
.mobile-menu {
	border: none;
	background: none;
	color: var(--dark);
	display: flex;
	align-items: center;
	justify-content: center;
}
.mobile-menu span {
	display: inline-block;
	margin-right: 6px;
}
.mobile-menu ion-icon {
	font-size: large;
	font-weight: light;
}
#mobile-loginPanel {
	position: fixed;
	top: 10vh;
	left: 0;
	z-index: 99999;
	transform: translateX(-100%);
	transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out;
	opacity: 0;
	width: 100%;
}

#mobile-loginPanel.open {
	transform: translateX(0);
	opacity: 1;
}

.mobile-menu ion-icon[name="menu-outline"] {
	display: block;
	font-size: x-large;
}

.mobile-menu ion-icon[name="close-outline"] {
	display: none;
	font-size: x-large;
}

#mobile-loginPanel.open + .mobile-menu ion-icon[name="menu-outline"] {
	display: none;
}

#mobile-loginPanel.open + .mobile-menu ion-icon[name="close-outline"] {
	display: block;
}

.no-session {
	background: var(--light);
	height: 90vh;
	backdrop-filter: blur(12px);
}
.with-session {
	background: var(--light);
	height: 90vh;
	width: 100%;
}
.with-session .small-container {
	border: 1px solid #ccc;
	padding: 24px;
	border-radius: 12px;
	height: calc(100% - 12px);
}
.no-session .small-container {
	border: 1px solid #ccc;
	padding: 24px;
	border-radius: 12px;
	height: calc(100% - 12px);
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	background: var(--light);
}
.mobile-intro {
	margin-bottom: 42px;
}
.no-session .form-item #error-message {
	width: 100% !important;
}

.mobile-intro h5 {
	margin-bottom: 12px;
}
.no-session .trouble-login {
	width: 100%;
}
.no-session .form-item {
	width: 100%;
}
.no-session .form-item input {
	width: 100%;
	padding: 8px 12px;
	border: 1px solid #ccc;
}
.no-session .form-item button {
	width: 100%;
	padding: 8px 12px;
	border-radius: 36px;
	border: none;
	background: var(--dark);
	color: var(--light);
}

/*-----Dashboard-Navigation ---*/
.with-session .small-container {
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
.dashboard-menu {
	width: 100%;
	display: flex;
	align-items: center;
	margin-top: 12px;
}
.dashboard-menu-item {
	flex: 1;
	text-align: center;
}
.dashboard-menu-item p {
	margin-top: 6px;
	font-size: 11px !important;
}
.dashboard-menu-item:first-child {
	margin-right: 6px;
}

#chart_div,
#chart_courses {
	padding: 0;
	margin: 0;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.progress-dash {
	aspect-ratio: 1/1;
	background: var(--list);
	border: 1px solid #ccc;
	border-radius: 12px;
	padding: 0;
	display: flex;
	align-items: center;
	justify-content: center;
}
.chart-wrapper {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

#chart_div,
#chart_courses {
	width: 100%;
	height: 100%;
	margin: 0 auto;
}

.with-session ul {
	flex: 1;
	margin-top: 24px;
	margin-bottom: 24px;
}
.with-session ul li {
	display: flex;
	align-items: center;
	padding: 8px 12px;
	color: var(--dark);
	font-size: small;
	border: 1px solid #ccc;
	border-radius: 36px;
}
.with-session ul li:not(:first-child) {
	margin-top: 6px;
}
.with-session ul li ion-icon {
	margin-right: 12px;
}
.with-session ul li a {
	font-size: small;
	color: var(--dark);
}

.session-out {
	width: 100%;
	display: flex;
	align-items: center;
	padding: 8px 12px;
	border-radius: 36px;
	background: var(--dark);
}
.session-out ion-icon {
	margin-right: 12px;
	color: var(--light);
}
.session-out a {
	color: var(--light);
	font-size: small;
	font-weight: 500;
	text-decoration: none;
}

/*-----Search Form----*/
header .search-form {
	width: 100%;
	display: flex;
	border: 1px solid #ccc;
	border-radius: 36px;
	align-items: center;
	font-family: "Open Sans";
}
header .search-form input {
	flex: 1;
	padding: 8px 16px;
	outline: none;
	border: none;
	background: transparent;
}
header .search-form button {
	background: var(--dark);
	color: var(--light);
	border-radius: 36px;
	padding: 4px 12px;
	transform: translateX(-3px);
	font-weight: 400;
	display: flex;
	align-items: center;
}
header .search-form button ion-icon {
	margin-right: 12px;
}
.loginBtn,
.signupBtn,
.logoutBtn,
.accountBtn {
	transition: all 0.2s ease-in-out;
	display: flex;
	align-items: center;
	border-radius: 16px;
}
.accountBtn {
	border-radius: 0;
	padding-left: 24px;
	border-left: 1px solid #ccc;
}
.accountBtn a {
	display: block;
	color: var(--dark);
	font-size: small;
}
.accountBtn:hover a {
	text-decoration: underline;
}
.accountBtn .initials {
	width: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	aspect-ratio: 1/1;
	background: var(--dark);
	border-radius: 50%;
	color: var(--light);
	font-weight: bold;
	margin-left: 0 !important;
	display: none;
}
.accountBtn .initials span {
	color: white;
}

.loginBtn {
	border: 1px solid var(--grey);
}

.loginBtn span,
.accountBtn span {
	color: var(--dark);
}
.signupBtn,
.logoutBtn {
	border: 1px solid var(--dark);
	background: var(--dark);
}
.signupBtn span,
.logoutBtn span {
	color: var(--light);
}
.loginBtn button,
.signupBtn button {
	display: flex;
	align-items: center;
	background: none;
	border: none;
	outline: none;
	white-space: nowrap;
	padding: 4px 8px;
}

.accountBtn button {
	padding-left: 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.loginBtn:hover,
.signupBtn:hover {
	background: var(--dark);
	border-color: transparent;
}
.logoutBtn:hover {
	background: var(--red);
	border: 1px solid var(--red);
}

.loginBtn:hover span {
	color: var(--light);
}

.logoutBtn span a,
.signupBtn span a {
	color: var(--light) !important;
	padding: 2px 12px;
	padding-bottom: 4px;
	display: block;
}
.loginBtn span {
	padding: 2px 12px;
}

/*----Login and Signup Panel------*/

.panel {
	position: fixed;
	top: 10vh;
	right: 5%;
	width: 90%;
	border-radius: 12px;
	overflow-y: auto;
	margin-right: -100%;
	transition: all 0.2s ease-in-out;
	z-index: 99999;
	background: transparent;
	display: flex;
	flex-direction: row-reverse;
	justify-content: space-between;
	height: calc(90vh - 24px);
}

/*---start-Verify Panel------*/
#verifyPanel {
	border: 1px solid #ccc !important;
	margin-right: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	height: calc(90vh - 24px) !important;
}

#verifyPanel form {
	height: auto !important;
	width: 380px;
	text-align: center;
}
#verifyPanel form h2 {
	color: var(--dark);
	text-align: center !important;
	width: 100%;
}
#verifyPanel form p {
	color: var(--grey);
	font-size: 13px;
	margin-top: 24px;
	margin-bottom: 12px;
}
#verifyPanel .form-item {
	flex-direction: column;
}
#verifyPanel form .form-item:not(:last-child) {
	margin-bottom: 24px;
}
#verifyPanel input {
	border: 1px solid #ccc;
}
#verifyPanel button {
	width: 100%;
	padding: 12px;
	border: none;
	background: var(--btn-blue);
	color: var(--light);
	border-radius: 36px;
	margin-top: 12px;
}
#verifyPanel img {
	width: 240px;
}
/*---end-Verify Panel------*/

.panel-input,
.panel-ads {
	width: calc(50% - 6px);
	border: 1px solid #ccc;
	padding: 42px;
	border-radius: 12px;
	position: relative;
	overflow: hidden;
	z-index: 9;
}

.panel-input {
	background: rgba(255, 255, 255, 0.9);
	padding: 12px;
	display: flex;
   backdrop-filter: blur(24px);
   box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.5);
   margin-right: 1px;
}
.panel-input .session-users {
	width: 80px;
	height: 100%;
	background: #f5f7fb;
	margin-right: 12px;
	border-radius: 16px;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 24px;
	padding-top: 42px;
}

.session-users h5 {
	margin-top: 36px;
	margin-bottom: 0 !important;
	font-family: "Courier New", Courier, monospace;
}
.session-users p {
	font-size: 12px;
	font-weight: 400;
	text-align: center;
	margin-top: 4px;
}
.session-users .current-course {
	width: 40px;
	text-align: center;
}
.session-users .current-course h5 {
	margin-top: 12px;
	text-align: center;
}
.session-users .current-course i {
	width: 40px;
	height: 40px;
	border-radius: 50%;
	background: var(--dark);
	line-height: 40px;
	color: var(--light);
	border: 1px solid white;
}

.panel-ads img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	position: absolute;
	top: 0;
	left: 0;
	z-index: -99;
}

.panel-ads {
	display: flex;
	align-items: end;
}

.session-panel {
	background: transparent;
	padding: 36px;
	border-radius: 16px;
	height: 100%;
	position: relative;
   width: calc(70% + 12px);
}

.session-panel input {
	border: 1px solid #ccc !important;
	background: none !important;
	width: 100% !important;
}
.session-intro {
	border-radius: 16px;
	margin-bottom: 12px;
	color: var(--light);
	margin-bottom: 42px;
	width: 100%;
}

.session-panel h5 {
	color: var(--dark);
	margin-bottom: 36px !important;
}
.session-panel p {
	color: var(--grey);
	font-size: small;
}
.session-panel .session-intro p {
   padding: 12px;
   padding-left: 24px;
   border-left: 4px solid var(--gold);
   background: rgb(255, 252, 213);
}
.session-panel a {
	color: var(--dark);
	text-decoration: underline;
}
.welcome-text {
	width: 90%;
}
.welcome-text ul li:not(.direct-email) {
	margin-bottom: 24px;
	color: var(--dark);
	padding-bottom: 4px;
	border-bottom: 1px solid #ccc;
}
.welcome-text ul li p {
	color: var(--grey);
	font-size: 13px;
	margin-top: 4px;
}
.welcome-text ul li a {
	color: var(--dark);
}
.direct-email {
	padding: 12px 24px;
	border-radius: 36px;
	display: flex;
	align-items: center;
	background: var(--dark);
	width: 100%;
}
.direct-email a {
	font-size: 13px;
	text-decoration: underline;
	color: var(--light);
}
.direct-email i {
	margin-right: 12px;
	color: var(--light);
}

.panel input {
	width: 100%;
	padding: 6px 12px;
	border-radius: 16px;
	border: none;
	background: none;
	position: relative;
	background: var(--light);
}
.panel .ver-submit {
	border: none;
	padding: 8px 12px;
	border-radius: 36px;
	font-weight: bold;
	width: 100%;
	background: var(--dark);
	color: var(--light);
	border: 1px solid #ccc;
	margin-top: 24px;
}
.panel select {
	background: var(--light) !important;
	outline: none;
}

.panel h5 {
	margin-bottom: 42px;
	font-size: large;
}

.arrow-back {
	outline: none;
	position: absolute;
	top: 22px;
	right: 22px;
	padding: 4px;
	font-size: x-large;
	z-index: 99;
	color: var(--dark);
	width: 50px;
	height: 50px;
	border: none;
	border-radius: 50%;
	background: var(--light);
}
.arrow-back:hover {
	background: rgba(219, 219, 219, 0.333);
}

/*----Forms------*/
.form-item {
	margin-top: 12px;
	display: flex;
	align-items: center;
	color: var(--light);
}
.form-item input {
	color: var(--dark);
	padding: 12px 24px;
	border-radius: 36px;
}

/* Password input container with toggle button */
.password-input-container {
	position: relative;
}

.password-input-container input {
	padding-right: 50px !important; /* Make room for the toggle button */
}

.password-toggle-btn {
	position: absolute;
	right: 12px;
	top: 50%;
	transform: translateY(-50%);
	background: none;
	border: none;
	cursor: pointer;
	padding: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #666;
	font-size: 18px;
}

.password-toggle-btn:hover {
	color: #333;
}

.password-toggle-btn ion-icon {
	font-size: 18px;
}

.form-item .first-item-f {
	margin-right: 6px;
}
.form-item .first-item-l {
	margin-left: 6px;
}

.select-item .label-item,
.select-item select {
	width: 50%;
	padding: 8px 24px;
	background: none;
}
.select-item {
	background: var(--light);
	border-radius: 36px;
	padding: 4px;
}
.select-item .label-item {
	margin-right: 6px;
}
.select-item .label-item label {
	font-size: 13px;
	color: var(--grey);
}
.select-item select {
	margin-left: 6px;
	cursor: pointer;
	color: var(--dark);
	background: none;
	border-radius: 36px;
	border: 1px solid #ccc;
}

/*----Alerts------*/
.alert-container {
	position: fixed;
	right: calc(5% + 42px);
	bottom: 8px;
	z-index: 9999;
}

.success,
.failed {
	border-radius: 36px;
	padding: 4px;
	font-size: small;
	display: flex;
	align-items: center;
	font-weight: 500;
	margin-top: 4px;
	z-index: 99999;
}
.success i,
.failed i {
	margin-right: 6px !important;
}
.success a,
.failed a {
	margin-left: 12px;
	color: var(--light);
	border-radius: 36px;
	padding: 0 12px;
	padding-bottom: 1px;
}
.success a {
	background: green;
	font-weight: 400;
}
.failed a {
	background: var(--red);
	font-weight: 400;
}

/*----Smart Floating Alerts------*/
.smart-alert-container {
	position: fixed;
	top: 20px;
	right: 20px;
	z-index: 10000;
	max-width: 400px;
}

.smart-alert {
	background: #fff;
	border-radius: 12px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
	margin-bottom: 12px;
	padding: 16px 20px;
	display: flex;
	align-items: flex-start;
	gap: 12px;
	transform: translateX(420px);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	border-left: 4px solid;
	position: relative;
	overflow: hidden;
}

.smart-alert.show {
	transform: translateX(0);
}

.smart-alert.hide {
	transform: translateX(420px);
	opacity: 0;
}

.smart-alert.success {
	border-left-color: #10b981;
	background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
}

.smart-alert.error {
	border-left-color: #ef4444;
	background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.smart-alert.warning {
	border-left-color: #f59e0b;
	background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.smart-alert.info {
	border-left-color: #3b82f6;
	background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
}

.smart-alert-icon {
	flex-shrink: 0;
	width: 24px;
	height: 24px;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	color: #fff;
	font-weight: bold;
}

.smart-alert.success .smart-alert-icon {
	background: #10b981;
}

.smart-alert.error .smart-alert-icon {
	background: #ef4444;
}

.smart-alert.warning .smart-alert-icon {
	background: #f59e0b;
}

.smart-alert.info .smart-alert-icon {
	background: #3b82f6;
}

.smart-alert-content {
	flex: 1;
	min-width: 0;
}

.smart-alert-title {
	font-weight: 600;
	font-size: 14px;
	margin: 0 0 4px 0;
	color: #1f2937;
	line-height: 1.4;
}

.smart-alert-message {
	font-size: 13px;
	color: #6b7280;
	margin: 0;
	line-height: 1.4;
	word-wrap: break-word;
}

.smart-alert-close {
	position: absolute;
	top: 8px;
	right: 8px;
	background: none;
	border: none;
	color: #9ca3af;
	cursor: pointer;
	padding: 4px;
	border-radius: 4px;
	transition: all 0.2s;
	font-size: 16px;
	line-height: 1;
	width: 24px;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.smart-alert-close:hover {
	background: rgba(0, 0, 0, 0.05);
	color: #374151;
}

.smart-alert-progress {
	position: absolute;
	bottom: 0;
	left: 0;
	height: 3px;
	background: rgba(0, 0, 0, 0.1);
	transition: width linear;
}

.smart-alert.success .smart-alert-progress {
	background: #10b981;
}

.smart-alert.error .smart-alert-progress {
	background: #ef4444;
}

.smart-alert.warning .smart-alert-progress {
	background: #f59e0b;
}

.smart-alert.info .smart-alert-progress {
	background: #3b82f6;
}

.success {
	background: rgb(201, 255, 201, 0.9);
	color: green;
}
.failed {
	background: rgb(255, 207, 207, 0.9);
	color: var(--red);
}
.header-nav-mobile {
	height: 10vh;
	display: none;
	position: fixed;
	top: 0;
	z-index: 9999;
	background: var(--light);
	width: 100%;
}

.message-box-container {
	width: 380px;
	border-radius: 16px;
	padding: 24px;
	padding-right: 0;
	position: fixed;
	right: calc(5% + 12px);
	bottom: 12px;
	z-index: 999999;
	max-height: 420px;
	height: 420px;
	display: block;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	background: var(--light);
	overflow: hidden;
   box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
   border: 1px solid #eee;
	/* display: block !important; */
}

.message-box-container h3 {
	margin-bottom: 24px;
	font-size: small;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

.message-box-container .search-bar {
	margin-bottom: 24px;
}
.message-box-container #search-recipient {
	width: 100%;
	padding: 8px 16px;
	border-radius: 36px !important;
	border: 1px solid #ccc;
}
.scroll-message-container {
	height: 95%;
	overflow-y: scroll;
	margin-bottom: 10vh;
}
.scroll-message-container::-webkit-scrollbar {
	width: 12px;
	background: transparent !important;
}
.scroll-message-container::-webkit-scrollbar-track {
	background: transparent !important;
}
.scroll-message-container::-webkit-scrollbar-thumb {
	background: transparent !important;
}

#friends-list::-webkit-scrollbar {
	background: transparent !important;
	height: 12px;
}
#friends-list::-webkit-scrollbar-track {
	background: transparent !important;
}
#friends-list::-webkit-scrollbar-thumb {
	background: transparent !important;
}

#friends-list {
	display: flex;
	overflow-x: scroll;
	overflow-y: hidden;
	flex-wrap: nowrap;
	position: relative;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	cursor: grab;
}

#friends-list li {
	margin-right: 12px;
	flex: 0 0 auto;
	width: calc(16.66666666666667% + 1.5px);
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
#friends-list li .circle-span {
	background: var(--dark);
	width: 100%;
	aspect-ratio: 1/1;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: small;
	font-weight: bold;
	color: var(--light);
	overflow: hidden;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
.circle-span img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	pointer-events: none;
}

#friends-list li span {
	display: block;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
#friends-list li .span-text {
	font-size: 12px;
	margin-top: 6px;
	text-align-last: center;
	color: var(--dark-grey);
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

#recent-messages {
	width: 100%;
	user-select: none;
}
#recent-messages li {
	margin-bottom: 12px;
	display: flex;
	align-items: center;
	user-select: none;
	padding-right: 12px;
	border-radius: 36px;
}
.img-circle-span {
	width: 42px;
	height: 42px;
	border-radius: 50%;
	overflow: hidden;
	margin-right: 12px;
	user-select: none;
}
.img-circle-span img {
	width: 100%;
	height: 100%;
	object-fit: cover;
	pointer-events: none;
}
.span-text-container {
	display: block;
	flex: 1;
	padding: 0 12px;
	padding-left: 0;
}
.span-text-container span {
	display: block;
}
.span-text-container .span-text {
	font-size: small;
	font-weight: 500;
	color: var(--dark);
	margin-bottom: 4px;
}
.span-text-container .span-message {
	font-size: 12px;
	font-weight: 400;
	color: var(--dark-grey);
}
.span-time {
	font-size: 12px;
}

/* chat-box header */

.chat-box {
	border: 1px solid #ccc;
	/* display: flex !important; */
	position: fixed;
	bottom: 8px;
	right: calc(380px + 5% + 12px);
	width: 380px;
	height: 420px;
	border-radius: 16px;
	background: var(--light);
	max-height: 420px;
	overflow: hidden;
	overflow-y: scroll;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	z-index: 99999;
	padding: 24px;
	padding-right: 12px;
	padding-top: 14px;
}

.chat-box::-webkit-scrollbar {
	background: transparent !important;
	width: 12px;
}
.chat-box::-webkit-scrollbar-track {
	background: transparent !important;
}
.chat-box::-webkit-scrollbar-thumb {
	background: transparent !important;
}
.chatbox-header {
	display: flex;
	align-items: center;
	font-size: small;
	font-weight: bold;
	margin-bottom: 12px;
}

.icon-circle {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	overflow: hidden;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	background: white;
	margin-right: 12px;
}
.chat-content {
	flex: 1;
	background: white;
	border-radius: 8px;
	display: flex;
	flex-direction: column;
	gap: 2px;
	overflow-y: auto;
	font-size: small;
	margin-top: 12px;
	margin-bottom: 12px;
	padding: 0 12px;
}
.chat-content::-webkit-scrollbar {
	background: transparent !important;
	width: 1px;
}
.chat-content::-webkit-scrollbar-track {
	background: #ccc !important;
}
.chat-content::-webkit-scrollbar-thumb {
	background: var(--btn-blue) !important;
}
.message-time {
	position: absolute;
	top: -26px;
	opacity: 0;
	transition: all 0.1s ease-in-out;
	z-index: 99;
	font-size: 12px;
	user-select: none;
	pointer-events: none;
}

/* Sent messages (right-aligned) */
.sent {
	align-self: flex-end;
	max-width: 90%;
	word-wrap: break-word;
	color: white;
	display: flex;
	flex-direction: row-reverse;
	align-items: center;
	position: relative;
}
.received {
	align-self: flex-start;
	max-width: 90%;
	word-wrap: break-word;
	color: white;
	display: flex;
	align-items: center;
	position: relative;
}
.sent .message-time {
	color: gray;
	white-space: nowrap;
	margin-right: 12px;
	border-radius: 36px;
	padding: 4px 8px;
	background: #f1f1f1;
}

/* Received messages (left-aligned) */

.received .message-time {
	color: gray;
	white-space: nowrap;
	margin-left: 12px;
	border-radius: 36px;
	padding: 4px 8px;
	background: #f1f1f1;
}
.received .message-text {
	background: #4e4e4e;
	font-size: small;
	padding: 5px 12px;
	padding-bottom: 6px;
	min-width: 96px;
}
.sent .message-text {
	background: var(--btn-blue);
	font-size: small;
	padding: 5px 12px;
	padding-bottom: 6px;
	min-width: 96px;
	font-family: "Open Sans", sans-serif;
}
/* Default border-radius for single-line messages */
.received .message-text,
.sent .message-text {
	border-radius: 36px;
	transition: border-radius 0.3s;
}

/* Smaller border-radius for multi-line messages */
.received.multi-line .message-text,
.sent.multi-line .message-text {
	border-radius: 8px;
}

.sent + .received,
.received + .sent {
	margin-top: 10px; /* Margin between send and received */
}

/* Remove margin between consecutive .send or .received messages */
.sent + .sent,
.received + .received {
	margin-top: 0;
}
.sent:hover .message-time,
.received:hover .message-time {
	opacity: 1;
}

.chat-send {
	display: flex;
	align-items: center;
	width: 100%;
	align-self: end;
}
.chat-send button {
	border: none;
	outline: none;
	background: none;
	font-size: large;
}

.chat-send textarea {
	flex: 1;
	border: none;
	outline: none;
	font-size: small;
	padding: 8px 12px;
	padding-right: 11px;
	padding-top: 7px;
	width: 100%;
	height: 30px;
	max-height: 120px;
	resize: none;
	overflow-y: auto;
	border-radius: 36px;
	border: 1px solid #ccc;
	margin-left: 12px;
	margin-right: 12px;
	transition: border-radius 0.2s;
}
.chat-send textarea::-webkit-scrollbar {
	background: transparent;
	width: 1px;
}
.chat-send textarea::-webkit-scrollbar-track {
	background: transparent !important;
}
.chat-send textarea::-webkit-scrollbar-thumb {
	background: transparent !important;
}

.attachments {
	display: flex;
	align-items: center;
}
.attachments button:first-child {
	margin-right: 12px;
}
#user-status {
	margin-left: 12px;
}
#status-span {
	font-weight: 400;
	font-size: 12px;
}
.chat-hide {
	position: absolute;
	top: 24px;
	right: 0;
	padding: 2px 8px;
	padding-bottom: 3px;
	font-size: 12px;
	border-radius: 36px;
	border: none;
	background: #f3f3f3;
	color: var(--dark);
	transition: all 0.2s ease-in-out;
}
.chat-hide:hover {
	color: white;
	background: rgb(255, 89, 89);
}

.chat-messagebox {
	position: absolute;
	top: 24px;
	right: 12px;
	padding: 2px 8px;
	padding-bottom: 3px;
	font-size: 12px;
	border-radius: 36px;
	border: none;
	background: #f3f3f3;
	color: var(--dark);
	transition: all 0.2s ease-in-out;
}
.chat-messagebox:hover {
	color: white;
	background: rgb(255, 89, 89);
}

.message-deleted {
	background: none !important;
	color: #b4b4b4;
	border: 1px solid #b4b4b4;
	font-style: italic;
	margin-top: 2px;
	font-size: 11px !important;
}
.delete-message-button {
	padding-right: 12px;
	background: none;
	border: none;
	font-size: 12px;
	color: #b4b4b4;
	opacity: 0;
	font-weight: bold;
	pointer-events: none;
}
.sent:hover .delete-message-button,
.received:hover .delete-message-button {
	opacity: 1;
	pointer-events: auto;
}
.delete-message-button:hover {
	color: #505050;
	text-decoration: underline;
}

.attachment-container {
	background: transparent;
	width: 100%;
}

#preview-container {
	display: flex;
	align-items: center;
	overflow-x: scroll;
	user-select: none;
	cursor: grab;
}
#preview-container img {
	user-select: none;
	object-fit: cover;
	width: 100%;
}
#preview-container:active {
	cursor: grabbing !important;
	background: transparent;
}
#preview-container::-webkit-scrollbar {
	background: transparent;
	height: 6px;
}
#preview-container::-webkit-scrollbar-track {
	background: transparent;
}
#preview-container::-webkit-scrollbar-thumb {
	background: transparent !important;
}
#preview-container .preview-wrapper {
	border: 1px solid #ececec;
	margin-right: 6px;
	position: relative;
	user-select: none;
	border-radius: 8px;
	height: 50px;
	font-size: 12px;
	background: #f1f1f1;
}

.preview-wrapper p {
	padding: 6px;
	text-overflow: ellipsis;
	overflow: hidden;
	display: -webkit-box;
	-webkit-line-clamp: 2 !important;
	line-clamp: 2 !important;
	-webkit-box-orient: vertical;
	white-space: normal;
	user-select: none !important;
	text-decoration: underline;
	font-size: 12px;
	max-height: 32px;
	color: var(--dark);
}

.preview-wrapper .remove-btn {
	position: absolute;
	top: 2px;
	right: 2px;
	border: none;
	background: var(--light);
	padding: 2px;
	color: var(--dark);
	aspect-ratio: 1/1;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: small;
}

.cancel-remove-btn-container {
	display: flex;
	justify-content: space-between;
	margin-bottom: 6px;
}

.cancel-remove-btn-container button {
	border: none;
	padding: 4px 12px;
	border-radius: 36px;
	background: #f3f3f3;
	color: var(--dark);
	transition: all 0.1s ease-in-out;
	font-size: 12px;
}
.cancel-remove-btn-container button:first-child {
	color: var(--light);
	background: var(--btn-blue);
	display: flex;
	align-items: center;
}
.cancel-remove-btn-container button ion-icon {
	transform: translateY(1px);
	margin-right: 4px;
}
.cancel-remove-btn-container button:last-child {
	background: none;
	padding: 0 !important;
}

.form-item #error-message {
   width: 70%; /* take full width for responsiveness */
   margin-top: 12px;
   min-height: 24px;
}

#error-message p {
   margin: 0;
   padding: 4px 16px;
   border-radius: 24px;
   font-family: "Open Sans", sans-serif;
   font-size: 13px;
   line-height: 1.5;
}
#error-message p:empty {
   display: none;
}
#error-message p:nth-child(2) {
   background: #ffe4e4;
   color: var(--btn-blue) !important; 
   border: 1px solid #f5c2c2;
}

.network-error-prompt {
   background: #ffe4e4;
   color: var(--btn-blue) !important; 
   border: 1px solid #f5c2c2;
}

.login-user-prompt {
   background: #dbffdb;
   color: #1c8a00 !important;
   border: 1px solid #a4e6a4;
}



.trouble-login {
	display: block;
	color: var(--dark);
	font-size: small;
	width: 100%;
	margin-top: 24px;
}
.trouble-login h4 {
	margin-bottom: 12px;
}
.trouble-login ol {
	list-style-type: decimal;
}
.trouble-login ol li {
	margin-bottom: 12px;
}
.reflective-content-limit {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: end;
	margin-top: 24px;
}
.button-container {
	display: flex;
	align-items: center;
	justify-content: end;
}
.button-container button {
	border: 1px solid var(--dark);
	background: var(--dark);
	color: var(--light);
	border-radius: 36px;
	padding: 4px 12px;
	font-size: small;
	margin-right: 12px;
	white-space: nowrap;
}
.button-container button:last-child {
	background: var(--light);
	color: var(--dark);
	border: 1px solid #ccc;
}
.button-container button p {
	display: flex;
	align-items: center;
	margin-bottom: 0;
	font-size: small;
	color: var(--light);
}
.button-container button p ion-icon {
	margin-right: 10px;
}

.chat-send .button-container {
	display: none;
}

.module-name-notif {
	border: none !important;
	padding: 0 !important;
	border-radius: 0 !important;
	display: inline !important;
	font-size: small !important;
	color: #4764ce;
	font-weight: bold;
}
.notification-retake {
	background: #f8d7da !important;
}
.notification-pass {
	background: #d4edda !important;
}
.search-container {
	position: relative;
}
#search-results-panel {
   display: none;
   z-index: 999;
}
.search-results-panel {
   position: absolute;
   background: var(--light);
   border: 1px solid #ccc;
   width: 100%;
   max-height: 30vh;
   overflow-y: auto;
   padding: 24px;
   top: calc(10vh - 12px);
   border-radius: 12px;
   box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.06);
}


.hidden {
   display: none;
}
.search-results-panel::-webkit-scrollbar {
	width: 0;
	height: 0;
}
.search-results-panel h3 {
	font-size: small;
	margin-bottom: 12px;
	color: green;
	position: relative;
	margin-left: 12px;
	font-weight: 400;
}
.search-results-panel h3::before {
	position: absolute;
	width: 4px;
	height: 4px;
	background: green;
	border-radius: 50%;
	z-index: 99999;
	top: 50%;
	left: -12px;
	transform: translateY(-50%);
	content: "";
	margin-right: 6px;
}

.search-results-panel ul {
	list-style: none;
	margin: 0;
	padding: 0;
	display: block;
	align-items: stretch;
	justify-content: baseline;
}

.search-results-panel li {
	padding: 8px 12px;
   padding-right: 4px;
	cursor: pointer;
	border: 1px solid #ccc;
	border-radius: 36px;
	font-weight: bold;
	color: var(--dark);
   background: var(--light);
   position: relative;
   display: flex;
   align-items: center;
   justify-content: space-between;
}

.search-results-panel.hidden {
   opacity: 0;
   pointer-events: none;
}

.search-results-panel li {
	margin-left: 0 !important;
	margin-bottom: 6px;
}

.search-results-panel li:hover {
	background: rgb(237, 255, 237);
   border-color: green;
   color: green;
}

@media screen and (max-width: 768px) {
	header {
		display: none;
	}
	.hero {
		margin-top: 10vh;
	}
	.header-margin {
		display: none;
	}
	.header-nav-mobile {
		display: flex;
	}
	.header-nav-mobile .small-container {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.header-item:nth-child(2) {
		display: none;
	}
	.header-item:last-child {
		flex: 1;
	}

	header .header-item:first-child button {
		display: none;
	}
	.menu {
		display: block;
	}
	.list-menu {
		display: block;
	}
	.message-notify {
		display: none;
	}
	.header-nav-item img {
		width: 120px;
	}
}
