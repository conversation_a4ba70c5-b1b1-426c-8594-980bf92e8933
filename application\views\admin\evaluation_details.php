<?php 
// ✅ Function to format duration in seconds into human-readable format
function formatDuration($seconds) {
   if ($seconds <= 0) return '0s';

   $days    = floor($seconds / 86400);
   $seconds -= $days * 86400;
   $hours   = floor($seconds / 3600);
   $seconds -= $hours * 3600;
   $minutes = floor($seconds / 60);
   $seconds -= $minutes * 60;

   $result = [];

   if ($days > 0) $result[] = $days . '' . ($days == 1 ? 'd' : 'd');
   if ($hours > 0) $result[] = $hours . '' . ($hours == 1 ? 'h' : 'h');
   if ($minutes > 0) $result[] = $minutes . '' . ($minutes == 1 ? 'm' : 'm');
   if ($seconds > 0) $result[] = $seconds . ' ' . ($seconds == 1 ? 'second' : 'seconds');

   return implode(' ', $result);
}
?>

<div class="main-container">

<?php
   $totalQuestions = 0;
   $correctAnswers = 0;

   if (!empty($module_details['sections'])) {
      foreach ($module_details['sections'] as $section) {
            if (!empty($section['combined_components'])) {
               foreach ($section['combined_components'] as $component) {
                  if ($component['component_group'] === 'multiple_choice' || $component['component_group'] === 'multiple_choice_set') {
                        $totalQuestions++;
                        if ($component['user_answer'] === $component['correct_answer']) {
                           $correctAnswers++;
                        }
                  }
               }
            }
      }
   }
?>


   <div class="evaluation-details-header">
      <h3>Evaluation Queue Details</h3>
      <a href="<?= base_url('evaluations') ?>">Back to Evaluation Queue</a>
   </div>

   <div class="evaluation-details-body">
      
      <div class="evaluation-body-item">
      <?php if (!empty($module_details['sections'])): ?>
            <?php foreach ($module_details['sections'] as $section): ?>
               <div class="evaluation-section">

                  <div class="section-header">
                        <div class="section-header-item">
                           <h4>Section</h4>
                           <h4><?= $section['section_name'] ?></h4>
                        </div>

                        <div class="section-header-item section-difficulty-container">
                           <h4>
                              <?php if (!empty($section['difficulty'])): ?>
                                    <p class="section-difficulty-p">
                                       <span>Difficulty</span>
                                       <span><?= ucfirst($section['difficulty']) ?>%</span>
                                    </p>
                              <?php endif; ?>
                           </h4>
                        </div>

                  </div>

                  <?php if (!empty($section['combined_components'])): ?>
                        <?php foreach ($section['combined_components'] as $component): ?>

                           <div class="component">
                              <!-- ✅ Component Header -->
                              <div class="component-title">
                                    <div class="component-item-header">
                                       <?php 
                                          $componentName = match ($component['component_group']) {
                                                'multiple_choice' => 'Multiple Choice',
                                                'multiple_choice_set' => 'Multiple Choice Set',
                                                'short_reading' => 'Short Reading',
                                                'short_video' => 'Short Video',
                                                'lecture_cast' => 'Lecture Cast',
                                                'podcast' => 'Podcast',
                                                default => ucfirst(str_replace('_', ' ', $component['component_type'] ?? ''))
                                          };
                                       ?>
                                       <p><?= $componentName ?></p>
                                       <p><?= $component['component_title'] ?? $component['question_text'] ?? '' ?></p>
                                    </div>

                                    <div class="component-item-header">
                                       <div class="mc-response">
                                          <?php if (isset($component['user_answer'], $component['correct_answer'])): ?>
                                                <?php if ($component['user_answer'] === $component['correct_answer']): ?>
                                                   <p class="correct-answer">
                                                      <span>Correct Response</span>
                                                   </p>
                                                <?php else: ?>
                                                   <p class="wrong-answer">
                                                      <span>Incorrect Response</span>
                                                   </p>
                                                <?php endif; ?>
                                          <?php endif; ?>
                                       </div>

                                       <?php if (!empty($component['time'])): ?>
                                          <p class="component-time">
                                                <span><ion-icon name="timer-outline"></ion-icon></span>
                                                <?= formatDuration($component['time']) ?>
                                          </p>
                                       <?php endif; ?>

                                       <p class="completed-status"><span>Completed</span></p>
                                    </div>

                                    
                              </div>

                              <!-- ✅ Reflective Writing / Short Reading / Short Video / Podcast -->
                              <?php if (!empty($component['component_question']) && $component['component_question'] !== 'N/A'): ?>
                                    <div class="component-content">
                                       <div class="content-item">
                                          <p class="q-a-context">Question</p> 
                                          <?= $component['component_question'] ?>
                                       </div>

                                       <div class="content-item narrative">
                                          <p class="q-a-context">User's Answer</p> 
                                          <p class="narrative-user-answer"><?= $component['user_answer'] ?></p>
                                       </div>

                                       <?php if (!empty($component['file_url'])): ?>
                                          <div class="content-item">
                                                <p class="q-a-context">File URL</p>
                                                <a href="<?= $component['file_url'] ?>" target="_blank">Download Attached File: <?= $component['file_url'] ?></a>
                                          </div>
                                       <?php endif; ?>
                                    </div>
                              <?php endif; ?>

                              <!-- ✅ Multiple Choice -->
                              <?php if ($component['component_group'] === 'multiple_choice'): ?>
                                    <?php if (!empty($component['question_text'])): ?>
                                       <div class="component-content">
                                          
                                          
                                          <div class="content-item">
                                                <p class="q-a-context">User's Answer</p>
                                                <p><?= $component['user_answer'] ?? 'N/A' ?></p>
                                          </div>

                                          <div class="content-item">
                                                <p class="q-a-context">Correct Answer</p>
                                                <p><?= $component['correct_answer'] ?? 'N/A' ?></p>
                                          </div>


                                       </div>
                                    <?php endif; ?>
                              <?php endif; ?>

                              <!-- ✅ Multiple Choice Set -->
                              <?php if ($component['component_group'] === 'multiple_choice_set'): ?>
                                    <?php if (!empty($component['question_text'])): ?>
                                       <div class="component-content">
                                          <div class="content-item">
                                                <p class="q-a-context">Question</p>
                                                <?= $component['question_text'] ?>
                                          </div>
                                          
                                          <div class="content-item">
                                                <p class="q-a-context">Your Answer</p>
                                                <?= $component['user_answer'] ?? 'N/A' ?>
                                          </div>

                                          <div class="content-item">
                                                <p class="q-a-context">Correct Answer</p>
                                                <p><?= $component['correct_answer'] ?? 'N/A' ?></p>
                                          </div>

                                          <div class="content-item">
                                                <?php if ($component['user_answer'] === $component['correct_answer']): ?>
                                                   <p class="correct-answer">
                                                      <span>Correct Response</span>
                                                </p>
                                                <?php else: ?>
                                                   <p class="wrong-answer">
                                                      <span>Incorrect Response</span>
                                                   </p>
                                                <?php endif; ?>
                                          </div>
                                          
                                       </div>
                                    <?php endif; ?>
                              <?php endif; ?>

                           </div>
                        <?php endforeach; ?>
                  <?php endif; ?>
               </div>
            <?php endforeach; ?>
      <?php else: ?>
            <p>No sections found for this module.</p>
      <?php endif; ?>

      <div class="retake-footer">
            <div class="retake-footer-item">
               <button id="markAsPassedButton" onclick="openMarkAsPassedModal()">Mark as Passed</button>
               <button onclick="openRetakeModal()">Retake Required</button>
            </div>
            <div class="retake-footer-item">
               <p class="initial-score">Initial Score: <?= $correctAnswers ?>/<?= $totalQuestions ?></p>
            </div>
      </div>

      </div>

      <div class="evaluation-body-item">
            <small>User's Module Details</small>
            <h4><?= htmlspecialchars($module_details['course_name'], ENT_QUOTES, 'UTF-8') ?></h4>
               <p>Description: <?= htmlspecialchars($module_details['description'], ENT_QUOTES, 'UTF-8') ?></p>

            <div class="p-table">
               <p>Employee: <span class="p-span"><?= htmlspecialchars($module_details['first_name'] . ' ' . $module_details['last_name'], ENT_QUOTES, 'UTF-8') ?></span></p>
               <?php
                  $dateStarted = !empty($module_details['date_started']) ? new DateTime($module_details['date_started']) : null;
                  $dateFinished = !empty($module_details['date_finished']) ? new DateTime($module_details['date_finished']) : null;                   
               ?>
               <p>Date Started: <span class="p-span"><?= $dateStarted ? $dateStarted->format('d/m/y') : 'ERROR' ?></span></p>
               <p>Date Finished: <span class="p-span"><?= $dateFinished ? $dateFinished->format('d/m/y') : 'ERROR' ?></span></p>

               <p>Module Duration: <span class="p-span">
                  <?= formatDuration(htmlspecialchars($module_details['module_duration'] ?? '', ENT_QUOTES, 'UTF-8')) ?>
               </span></p>

            </div>

      </div>

   </div>

   <!-- ✅ Retake Button -->


   <div id="retake-modal" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background-color:rgba(0,0,0,0.5); z-index:999;">
      <div class="modal-center">
            <h3><ion-icon name="repeat-outline"></ion-icon>Retake Module</h3>
            <p>Request a retake for this module. Provide feedback to improve the experience.</p>
            <textarea id="retake-feedback" placeholder="Enter your feedback..." style="width:100%; height:100px;" required></textarea>
            <p>Click<span class="detail-space"><u>Request Retake</u></span> to reset the module and clear previous progress.</p>
            <div class="modal-buttons">
               <button onclick="submitRetakeModule()">Request Retake</button>
               <button onclick="closeRetakeModal()">Cancel</button>
            </div>
      </div>
   </div>

   <!-- ✅ Mark as Passed Modal -->
   <div id="mark-as-passed-modal" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background-color:rgba(0,0,0,0.5); z-index:999;">
      <div class="modal-center">
            <h3><ion-icon name="trophy-outline"></ion-icon>Mark as Passed</h3>
            <p>Mark this module as passed. Provide any feedback on performance.</p>
            <textarea id="mark-as-passed-feedback" placeholder="Enter your feedback..." style="width:100%; height:100px;" required></textarea>
            <p>Click<span class="detail-space"><u>Mark as Passed</u></span> to confirm completion and issue a certificate.</p>
            <div class="modal-buttons">
               <button onclick="submitMarkAsPassed()">Mark as Passed</button>
               <button onclick="closeMarkAsPassedModal()">Cancel</button>
            </div>
      </div>
   </div>



</div>





<script>
   function openRetakeModal() {
      document.getElementById('retake-modal').style.display = 'flex';
   }

   function closeRetakeModal() {
      document.getElementById('retake-modal').style.display = 'none';
   }

   function openMarkAsPassedModal() {
      document.getElementById('mark-as-passed-modal').style.display = 'flex';
   }

   // ✅ Close the "Mark as Passed" modal
   function closeMarkAsPassedModal() {
      document.getElementById('mark-as-passed-modal').style.display = 'none';
   }

   function submitRetakeModule() {
      const feedback = document.getElementById('retake-feedback').value.trim();
      const moduleDuration = '<?= htmlspecialchars($module_details['module_duration'], ENT_QUOTES, 'UTF-8') ?>';
   
      if (!feedback) {
            SmartAlerts.warning('Please provide feedback before retaking the module.', 'Feedback Required');
            return;
      }
   
      fetch('<?= base_url('admin/retake_module') ?>', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
               user_id: <?= $user_id ?>,
               module_id: <?= $module_id ?>,
               feedback: feedback,
               module_duration: moduleDuration // Include module_duration
            })
      })
      .then(response => response.json())
      .then(data => {
            SmartAlerts.success(data.message || 'Module has been reset successfully.', 'Module Reset');
            setTimeout(() => {
               window.location.href = '<?= base_url('admin/evaluations') ?>';
            }, 1500);
      });
   
      closeRetakeModal();
   }

   function submitMarkAsPassed() {
      const feedback = document.getElementById('mark-as-passed-feedback').value.trim();
      const moduleDuration = '<?= htmlspecialchars($module_details['module_duration'], ENT_QUOTES, 'UTF-8') ?>';

      if (!feedback) {
            SmartAlerts.warning('Please provide feedback before marking the module as passed.', 'Feedback Required');
            return;
      }

      fetch('<?= base_url('admin/mark_as_passed') ?>', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
               user_id: <?= $user_id ?>,
               module_id: <?= $module_id ?>,
               feedback: feedback,
               module_duration: moduleDuration
            })
      })
      .then(response => response.json())
      .then(data => {
            SmartAlerts.success(data.message || 'Module marked as passed.', 'Module Updated');
            setTimeout(() => {
               window.location.href = '<?= base_url('admin/evaluations') ?>';
            }, 1500);
      });

      closeMarkAsPassedModal();
   }
</script>

<script>
   document.addEventListener('DOMContentLoaded', () => {
      const components = document.querySelectorAll('.component');

      components.forEach(component => {
            const typeElement = component.querySelector('.component-item-header p:first-child');
            if (typeElement) {
               const typeText = typeElement.innerText.trim().toLowerCase();

               switch (typeText) {
                  case 'multiple choice':
                        component.style.backgroundColor = '#ffcccc'; // Light Red
                        component.style.color = '#900';
                        break;
                  case 'reflective writing':
                        component.style.backgroundColor = '#cce5ff'; // Light Blue
                        component.style.color = '#004085';
                        break;
                  case 'multiple choice set':
                        component.style.backgroundColor = '#d4edda'; // Light Green
                        component.style.color = '#155724';
                        break;
                  case 'short reading':
                        component.style.backgroundColor = '#fff3cd'; // Light Yellow
                        component.style.color = '#856404';
                        component.style.borderRadius = '36px';
                        component.style.padding = '8px 12px';
                        break;
                  case 'short video':
                        component.style.backgroundColor = '#f8d7da'; // Light Pink
                        component.style.color = '#721c24';
                        component.style.borderRadius = '36px';
                        component.style.padding = '8px 12px';
                        break;
                  case 'lecture cast':
                        component.style.backgroundColor = '#d1ecf1'; // Light Cyan
                        component.style.color = '#0c5460';
                        component.style.borderRadius = '36px';
                        component.style.padding = '8px 12px';
                        break;
                  case 'podcast':
                        component.style.backgroundColor = '#eaf7e1'; // Pale sage
                        component.style.color = '#2e7d32';
                        component.style.borderRadius = '36px';
                        component.style.padding = '8px 12px';
                        break;
                  default:
                        component.style.backgroundColor = '#e2e3e5'; // Light Gray (default)
                        component.style.color = '#383d41';
                        break;
               }
            }
      });
   });

   // Get the URL parameters
   const urlParams = new URLSearchParams(window.location.search);
   const source = urlParams.get('source');

   // If the source is blocked_retakers, hide the button
   if (source === 'blocked_retakers') {
      const button = document.getElementById('markAsPassedButton');
      if (button) {
         button.style.display = 'none';
      }
   }

</script>
