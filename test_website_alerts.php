<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Website Alerts</title>
    
    <!-- Include your CSS -->
    <link rel="stylesheet" href="css/header.css">
    
    <!-- JQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    
    <!-- Smart Alerts System -->
    <script src="assets/js/smart-alerts-simple.js"></script>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 40px;
            background: #f8fafc;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        button {
            padding: 10px 20px;
            margin: 8px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
        }
        
        h1 { color: #1f2937; }
        h3 { color: #374151; margin-bottom: 15px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Website Smart Alerts Test</h1>
        <p>Testing smart alerts in your website environment</p>
        
        <div class="test-section">
            <h3>Message System Test</h3>
            <p>Simulating the message sending functionality:</p>
            <button class="btn-warning" onclick="testMessageValidation()">Test: Send Message Without Recipients</button>
            <button class="btn-success" onclick="testMessageSuccess()">Test: Message Sent Successfully</button>
        </div>
        
        <div class="test-section">
            <h3>Draft System Test</h3>
            <p>Simulating the draft functionality:</p>
            <div class="form-group">
                <label>Test Textarea:</label>
                <textarea id="testTextarea" placeholder="Type something here..."></textarea>
            </div>
            <button class="btn-primary" onclick="saveDraft()">Save Draft</button>
            <button class="btn-primary" onclick="loadDraft()">Load Draft</button>
            <button class="btn-warning" onclick="loadNonExistentDraft()">Load Non-existent Draft</button>
        </div>
        
        <div class="test-section">
            <h3>Notification System Test</h3>
            <p>Simulating notification interactions:</p>
            <button class="btn-success" onclick="testNotificationSuccess()">Test: Notification Marked as Read</button>
            <button class="btn-danger" onclick="testNotificationError()">Test: Notification Update Failed</button>
            <button class="btn-danger" onclick="testNetworkError()">Test: Network Error</button>
        </div>
        
        <div class="test-section">
            <h3>Admin Evaluation Test</h3>
            <p>Simulating admin evaluation actions:</p>
            <button class="btn-warning" onclick="testFeedbackRequired()">Test: Feedback Required</button>
            <button class="btn-success" onclick="testModuleReset()">Test: Module Reset Success</button>
            <button class="btn-success" onclick="testModulePassed()">Test: Module Marked as Passed</button>
        </div>
        
        <div class="test-section">
            <h3>Course System Test</h3>
            <p>Simulating course interactions:</p>
            <button class="btn-warning" onclick="testComponentSelection()">Test: Component Selection Required</button>
            <button class="btn-success" onclick="testEnrollmentSuccess()">Test: Enrollment Success</button>
            <button class="btn-danger" onclick="testEnrollmentError()">Test: Enrollment Error</button>
        </div>
        
        <div class="test-section">
            <h3>Upload System Test</h3>
            <p>Simulating file upload scenarios:</p>
            <button class="btn-danger" onclick="testUploadError()">Test: Upload Error</button>
            <button class="btn-danger" onclick="testNetworkUploadError()">Test: Network Upload Error</button>
        </div>
        
        <div class="test-section">
            <h3>Original Alert Override Test</h3>
            <p>Testing the alert() function override:</p>
            <button class="btn-primary" onclick="testOriginalAlert()">Test: Original alert() Function</button>
            <button class="btn-primary" onclick="testMultipleAlerts()">Test: Multiple Alerts</button>
        </div>
    </div>

    <script>
        // Wait for everything to load
        $(document).ready(function() {
            console.log('Document ready');
            console.log('SmartAlerts available:', typeof window.SmartAlerts);
            
            // Test if SmartAlerts is working
            setTimeout(function() {
                if (window.SmartAlerts) {
                    SmartAlerts.info('Smart Alerts system is ready!', 'System Ready');
                } else {
                    alert('SmartAlerts failed to load!');
                }
            }, 1000);
        });
        
        // Message System Tests
        function testMessageValidation() {
            SmartAlerts.warning("Please select at least one recipient before sending the message.", "Recipients Required");
        }
        
        function testMessageSuccess() {
            SmartAlerts.success("Message sent successfully to all recipients.", "Message Sent");
        }
        
        // Draft System Tests
        function saveDraft() {
            var content = document.getElementById('testTextarea').value;
            if (content.trim()) {
                localStorage.setItem('test_draft', content);
                SmartAlerts.success('Draft saved successfully!', 'Draft Saved');
            } else {
                SmartAlerts.warning('Please enter some content to save as draft.', 'No Content');
            }
        }
        
        function loadDraft() {
            var savedValue = localStorage.getItem('test_draft');
            if (savedValue !== null) {
                document.getElementById('testTextarea').value = savedValue;
                SmartAlerts.success('Draft restored successfully!', 'Draft Loaded');
            } else {
                SmartAlerts.info('No draft found for this field.', 'No Draft Available');
            }
        }
        
        function loadNonExistentDraft() {
            SmartAlerts.info('No draft found for this field.', 'No Draft Available');
        }
        
        // Notification Tests
        function testNotificationSuccess() {
            SmartAlerts.success('Notification has been marked as read.', 'Notification Updated');
        }
        
        function testNotificationError() {
            SmartAlerts.error('Unable to mark notification as read. Please try again.', 'Update Failed');
        }
        
        function testNetworkError() {
            SmartAlerts.error('A network error occurred while updating the notification.', 'Connection Error');
        }
        
        // Admin Evaluation Tests
        function testFeedbackRequired() {
            SmartAlerts.warning('Please provide feedback before retaking the module.', 'Feedback Required');
        }
        
        function testModuleReset() {
            SmartAlerts.success('Module has been reset successfully.', 'Module Reset');
        }
        
        function testModulePassed() {
            SmartAlerts.success('Module marked as passed.', 'Module Updated');
        }
        
        // Course System Tests
        function testComponentSelection() {
            SmartAlerts.warning('Please select at least one component to proceed with the course.', 'Selection Required');
        }
        
        function testEnrollmentSuccess() {
            SmartAlerts.success('Successfully enrolled in the course!', 'Enrollment Complete');
        }
        
        function testEnrollmentError() {
            SmartAlerts.error('Failed to enroll in the course. Please try again.', 'Enrollment Failed');
        }
        
        // Upload Tests
        function testUploadError() {
            SmartAlerts.error('An error occurred while uploading: File too large', 'Upload Failed');
        }
        
        function testNetworkUploadError() {
            SmartAlerts.error('A network error occurred. Please check your connection and try again.', 'Network Error');
        }
        
        // Original Alert Tests
        function testOriginalAlert() {
            alert('This should show as a smart alert instead of browser alert!');
        }
        
        function testMultipleAlerts() {
            SmartAlerts.info('First alert', 'Alert 1');
            setTimeout(() => SmartAlerts.success('Second alert', 'Alert 2'), 500);
            setTimeout(() => SmartAlerts.warning('Third alert', 'Alert 3'), 1000);
            setTimeout(() => SmartAlerts.error('Fourth alert', 'Alert 4'), 1500);
        }
    </script>
</body>
</html>
