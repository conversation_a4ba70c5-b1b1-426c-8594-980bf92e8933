<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Smart Alerts</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            font-weight: 500;
        }
        .status.success { background: #d1fae5; color: #065f46; }
        .status.error { background: #fee2e2; color: #991b1b; }
        .status.warning { background: #fef3c7; color: #92400e; }
        .status.info { background: #dbeafe; color: #1e40af; }
        
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
        }
        .btn-test { background: #3b82f6; color: white; }
        .btn-old { background: #6b7280; color: white; }
        
        .log {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 15px 0;
        }
        
        h1 { color: #1f2937; }
        h3 { color: #374151; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 Smart Alerts Debug</h1>
        
        <div id="loadStatus" class="status warning">Checking Smart Alerts...</div>
        
        <h3>Debug Information</h3>
        <div id="debugLog" class="log">Loading...</div>
        
        <h3>Test Buttons</h3>
        <button class="btn-test" onclick="testSmartAlert()">Test Smart Alert</button>
        <button class="btn-old" onclick="testOldAlert()">Test Old Alert</button>
        <button class="btn-test" onclick="testDirectCall()">Test Direct Call</button>
        
        <h3>Manual Test</h3>
        <p>Open browser console (F12) to see detailed logs.</p>
    </div>

    <!-- Inline Smart Alerts for debugging -->
    <script>
        // Debug logging
        function log(message) {
            console.log('[SmartAlerts Debug]', message);
            const logDiv = document.getElementById('debugLog');
            logDiv.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        log('Starting debug...');
        
        // Simple inline Smart Alerts implementation for debugging
        (function() {
            'use strict';
            
            log('Creating SmartAlerts class...');
            
            function SmartAlerts() {
                this.container = null;
                this.alertCounter = 0;
                this.init();
            }

            SmartAlerts.prototype.init = function() {
                log('Initializing SmartAlerts...');
                
                // Create container
                this.container = document.createElement('div');
                this.container.className = 'smart-alert-container';
                this.container.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    max-width: 400px;
                `;
                document.body.appendChild(this.container);
                log('Container created and added to body');
            };

            SmartAlerts.prototype.show = function(message, type, title, duration) {
                log('show() called with: ' + message + ', type: ' + type);
                
                type = type || 'info';
                title = title || this.getDefaultTitle(type);
                duration = duration || 5000;
                
                var alertId = ++this.alertCounter;
                var alertElement = this.createAlert(alertId, message, type, title);
                
                this.container.appendChild(alertElement);
                log('Alert element added to container');
                
                // Show animation
                setTimeout(function() {
                    alertElement.style.transform = 'translateX(0)';
                    log('Show animation triggered');
                }, 10);
                
                // Auto hide
                if (duration > 0) {
                    setTimeout(function() {
                        alertElement.style.transform = 'translateX(420px)';
                        alertElement.style.opacity = '0';
                        setTimeout(function() {
                            if (alertElement.parentNode) {
                                alertElement.parentNode.removeChild(alertElement);
                                log('Alert removed from DOM');
                            }
                        }, 300);
                    }, duration);
                }
                
                return alertId;
            };

            SmartAlerts.prototype.createAlert = function(id, message, type, title) {
                log('Creating alert element...');
                
                var alert = document.createElement('div');
                alert.className = 'smart-alert smart-alert-' + type;
                alert.style.cssText = `
                    background: #fff;
                    border-radius: 12px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
                    margin-bottom: 12px;
                    padding: 16px 20px;
                    display: flex;
                    align-items: flex-start;
                    gap: 12px;
                    transform: translateX(420px);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    border-left: 4px solid ${this.getColor(type)};
                    position: relative;
                    overflow: hidden;
                `;
                
                var icon = this.getIcon(type);
                
                alert.innerHTML = `
                    <div style="
                        flex-shrink: 0;
                        width: 24px;
                        height: 24px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 14px;
                        color: #fff;
                        font-weight: bold;
                        background: ${this.getColor(type)};
                    ">${icon}</div>
                    <div style="flex: 1; min-width: 0;">
                        <div style="
                            font-weight: 600;
                            font-size: 14px;
                            margin: 0 0 4px 0;
                            color: #1f2937;
                            line-height: 1.4;
                        ">${title}</div>
                        <div style="
                            font-size: 13px;
                            color: #6b7280;
                            margin: 0;
                            line-height: 1.4;
                            word-wrap: break-word;
                        ">${message}</div>
                    </div>
                    <button onclick="this.parentNode.style.display='none'" style="
                        position: absolute;
                        top: 8px;
                        right: 8px;
                        background: none;
                        border: none;
                        color: #9ca3af;
                        cursor: pointer;
                        padding: 4px;
                        border-radius: 4px;
                        font-size: 16px;
                        line-height: 1;
                        width: 24px;
                        height: 24px;
                    ">&times;</button>
                `;
                
                log('Alert HTML created');
                return alert;
            };

            SmartAlerts.prototype.getColor = function(type) {
                var colors = {
                    success: '#10b981',
                    error: '#ef4444',
                    warning: '#f59e0b',
                    info: '#3b82f6'
                };
                return colors[type] || colors.info;
            };

            SmartAlerts.prototype.getIcon = function(type) {
                var icons = {
                    success: '✓',
                    error: '✕',
                    warning: '⚠',
                    info: 'i'
                };
                return icons[type] || icons.info;
            };

            SmartAlerts.prototype.getDefaultTitle = function(type) {
                var titles = {
                    success: 'Success',
                    error: 'Error',
                    warning: 'Warning',
                    info: 'Information'
                };
                return titles[type] || titles.info;
            };

            // Convenience methods
            SmartAlerts.prototype.success = function(message, title) {
                return this.show(message, 'success', title, 4000);
            };

            SmartAlerts.prototype.error = function(message, title) {
                return this.show(message, 'error', title, 6000);
            };

            SmartAlerts.prototype.warning = function(message, title) {
                return this.show(message, 'warning', title, 5000);
            };

            SmartAlerts.prototype.info = function(message, title) {
                return this.show(message, 'info', title, 5000);
            };

            // Create global instance
            log('Creating global SmartAlerts instance...');
            window.SmartAlerts = new SmartAlerts();
            
            // Override alert function
            log('Overriding alert function...');
            window.originalAlert = window.alert;
            window.alert = function(message) {
                log('alert() called with: ' + message);
                
                var type = 'info';
                var title = '';
                
                var msg = message.toLowerCase();
                if (msg.includes('error') || msg.includes('failed') || msg.includes('network')) {
                    type = 'error';
                    title = 'Error';
                } else if (msg.includes('success') || msg.includes('completed') || msg.includes('marked as')) {
                    type = 'success';
                    title = 'Success';
                } else if (msg.includes('please') || msg.includes('select') || msg.includes('provide')) {
                    type = 'warning';
                    title = 'Action Required';
                }
                
                window.SmartAlerts.show(message, type, title);
            };
            
            log('SmartAlerts initialization complete!');
            
            // Update status
            setTimeout(function() {
                const statusDiv = document.getElementById('loadStatus');
                if (window.SmartAlerts) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✓ SmartAlerts loaded successfully!';
                    log('Status updated to success');
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '✗ SmartAlerts failed to load';
                    log('Status updated to error');
                }
            }, 100);

        })();
        
        // Test functions
        function testSmartAlert() {
            log('testSmartAlert() called');
            if (window.SmartAlerts) {
                SmartAlerts.success('This is a test smart alert!', 'Test Success');
            } else {
                log('ERROR: SmartAlerts not available');
            }
        }
        
        function testOldAlert() {
            log('testOldAlert() called');
            alert('This should show as a smart alert!');
        }
        
        function testDirectCall() {
            log('testDirectCall() called');
            if (window.SmartAlerts) {
                window.SmartAlerts.show('Direct call test', 'info', 'Direct Test', 3000);
            } else {
                log('ERROR: SmartAlerts not available for direct call');
            }
        }
        
        log('Debug script loaded completely');
    </script>
</body>
</html>
